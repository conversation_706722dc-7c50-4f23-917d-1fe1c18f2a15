name: Deploy Main API to App Runner

on:
  push:
    branches:
      - dev
      - staging
      - main
  workflow_dispatch:



jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    environment: ${{ github.ref_name == 'main' && 'production' || github.ref_name == 'staging' && 'staging' || 'dev' }}

    steps:
      - name: Check out
        uses: actions/checkout@v3

      - name: Configure AWS
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}

      - name: Log in to ECR
        run: |
          aws ecr get-login-password --region ${{ vars.AWS_DEFAULT_REGION }} \
          | docker login --username AWS --password-stdin ${{ vars.AWS_ECR_REGISTRY }}

      - name: Build Docker image
        run: | 
          docker build \
          -t main-api \
          --build-arg "AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }}" \
          --build-arg "AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }}" \
          --build-arg "AWS_REGION=${{ vars.AWS_DEFAULT_REGION }}" \
          --build-arg "MIXPANEL_TOKEN=${{ secrets.MIXPANEL_TOKEN }}" \
          --build-arg "LEGACY_PREDICTION_URL=${{ vars.LEGACY_PREDICTION_URL }}" \
          --build-arg "ANDROMEDA_PREDICTION_URL=${{ vars.ANDROMEDA_PREDICTION_URL }}" \
          --build-arg "ANDROMEDA_PREDICTION_URL_TESTING=${{ vars.ANDROMEDA_PREDICTION_URL_TESTING }}" \
          --build-arg "MONGODB_CONNECTION_STRING=${{ secrets.MONGODB_CONNECTION_STRING }}" \
          --build-arg "APP_ENVIRONMENT=${{ github.ref_name == 'main' && 'production' || github.ref_name == 'staging' && 'staging' || 'dev' }}" \
          --build-arg "DEPLOY_TIMESTAMP=$(date +"%Y-%m-%d %H:%M:%S")" \
          --build-arg "JWT_PRIVATE_KEY=${{ secrets.JWT_PRIVATE_KEY }}" \
          --build-arg "JWT_PUBLIC_KEY=${{ vars.JWT_PUBLIC_KEY }}" \
          --build-arg "GOOGLE_CLIENT_ID=${{ vars.GOOGLE_CLIENT_ID }}" \
          --build-arg "APPLE_CLIENT_ID=${{ vars.APPLE_CLIENT_ID }}" \
          --build-arg "TEMPDROP_CLIENT_ID=${{ vars.TEMPDROP_CLIENT_ID }}" \
          --build-arg "TEMPDROP_CLIENT_SECRET=${{ secrets.TEMPDROP_CLIENT_SECRET }}" \
          --build-arg "KLAVIYO_API_KEY=${{ secrets.KLAVIYO_API_KEY }}" \
          .

      - name: Tag Docker image
        run: |
          docker tag main-api:latest \
          ${{ vars.AWS_ECR_REGISTRY }}/${{ vars.ECR_REPOSITORY }}:${{ github.sha }}

      - name: Push Docker image
        run: docker push ${{ vars.AWS_ECR_REGISTRY }}/${{ vars.ECR_REPOSITORY }}:${{ github.sha }}

      - name: Update App Runner Service
        run: |
          aws apprunner update-service \
            --service-arn ${{ vars.APPRUNNER_ARN }} \
            --source-configuration "ImageRepository={
              ImageIdentifier=${{ vars.AWS_ECR_REGISTRY }}/${{ vars.ECR_REPOSITORY }}:${{ github.sha }},
              ImageConfiguration={
                Port=80
              },
              ImageRepositoryType=ECR
            }"
