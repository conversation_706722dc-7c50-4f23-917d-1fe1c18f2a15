{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "node",
            "runtimeExecutable": "proxychains",
            "runtimeArgs": [
                "/home/<USER>/KEGGPROJECTS/api/node_modules/.bin/nodemon",
                "--watch", "src",
                "--ext", "ts",
                "--exec", "node -r tsconfig-paths/register -r ts-node/register"
            ],
            "restart": true,
            "request": "launch",
            "name": "Launch Program",
            "program": "${workspaceFolder}/src/app.ts",
            "preLaunchTask": "tsc: build - tsconfig.json",
            "outFiles": [
                "${workspaceFolder}/dist/**/*.js"
            ],
            "env": {
                "AWS_ACCESS_KEY_ID": "********************",
                "PORT": "8080",
                "AWS_SECRET_ACCESS_KEY": "OQQwP9b4nvx4sv2aF7wmrZVnGNB6N3jGAIJqwa7X",
                "AWS_REGION": "us-east-2",
                "MIXPANEL_TOKEN": "hovado",
                "LEGACY_PREDICTION_URL": "http://127.0.0.1:7777",
                "ANDROMEDA_PREDICTION_URL": "http://127.0.0.1:5000",
                "ANDROMEDA_PREDICTION_URL_TESTING": "http://127.0.0.1:5000",
                //"MONGODB_CONNECTION_STRING": "mongodb://kegg:hco87r3fgqwyicgkdjsycg23%23!1@************:27017/kegg?tls=false&authSource=kegg",
                "MONGODB_CONNECTION_STRING": "***************************************************************",
                //"MONGODB_CONNECTION_STRING": "mongodb://keggstaging:<EMAIL>:27017/kegg?tls=true&readPreference=secondaryPreferred&retryWrites=false&authMechanism=SCRAM-SHA-1",
                "APP_ENVIRONMENT": "production",
                "KLAVIYO_API_KEY": "*************************************",
                "DEPLOY_TIMESTAMP": "2025-01-01T00:00:00Z",
                "TEMPDROP_CLIENT_ID": "935eb5ce-a5e0-4c83-be22-256d0fd2e6eb",
                "TEMPDROP_CLIENT_SECRET": "Dp4NpN7RzUR7",
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
                "JWT_PUBLIC_KEY": "-----BEGIN PUBLIC KEY-----\nMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAn5KZ/YkcIeXoaG93KbtB\nj9aFX+2Pb53Xk654lXiFGNTl5Ic5P1UIIo1Vqd5IOiGFkP6QxYysnqQ73yJKkxSZ\nhvPP7/jezUnPturiSxdJw3bh7b1P0gjQvXG/CVPO2Mq4eIzRc/I0XBna5wWEIyZc\nLch6yYBnEiZCc7SYWYiUmSJ0pimEpOwpueqtBc25A7e1LoM0VB2BWTNfcLh9z/83\ncEu+t2EgTphfxK/kHFDhwFEODQT8u+2RvH2vHRqff3rdGH2x2mZGiU9RLmJtzjrs\n/kZmnU1MQH+EuvZ6iyVIEnWbCQHmWVolpQnigkUYAhiVbnPipX+RVbROmwvdH87g\nMMqVG12gkhNVTPNBHUNS/NSCxbfArwJIpEBa6pvO5noxYliXkaJNe5hgGf1D54f7\nHA91BYFsyZVNXrcNmkgz6qAjoD4EGUsWIR9SJZTHASsshc6iluBNMZM+Kxkxw3LG\ngyPFIXkFVY+9pQqNuqWJnhmXKKmFOeuq1WZ9kN2WBmE8QKNbbw8YWIZ+dWONxWY8\nF8J9JigW56tUuRVBOqETT3AHwhyNmeBL1QF+Hs/Twi8XVLIyR5QGc9SQh9N/LlHH\nPTLKbU5jzH8Iod42Mw3PYDKolIcIO4nJayxNjaF3z/XCbfZRSYqTXeqCSPsmEpig\nBnLfYLtkOPDkQ337vtBMPs8CAwEAAQ==\n-----END PUBLIC KEY-----"
            }
        }
    ]
}