FROM node:latest

# Accepting build arguments
ARG AWS_ACCESS_KEY_ID
ARG AWS_SECRET_ACCESS_KEY
ARG AWS_REGION
ARG MIXPANEL_TOKEN
ARG LEGACY_PREDICTION_URL
ARG ANDROMEDA_PREDICTION_URL
ARG ANDROMEDA_PREDICTION_URL_TESTING
ARG MONGODB_CONNECTION_STRING
ARG APP_ENVIRONMENT
ARG DEPLOY_TIMESTAMP
ARG JWT_PRIVATE_KEY
ARG JWT_PUBLIC_KEY
ARG GOOGLE_CLIENT_ID
ARG APPLE_CLIENT_ID
ARG TEMPDROP_CLIENT_ID
ARG TEMPDROP_CLIENT_SECRET
ARG KLAVIYO_API_KEY

# Baking into the image 
ENV AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
ENV AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
ENV AWS_REGION=${AWS_REGION}
ENV MIXPANEL_TOKEN=${MIXPANEL_TOKEN}
ENV LEGACY_PREDICTION_URL=${LEGACY_PREDICTION_URL}
ENV ANDROMEDA_PREDICTION_URL=${ANDROMEDA_PREDICTION_URL}
ENV ANDROMEDA_PREDICTION_URL_TESTING=${ANDROMEDA_PREDICTION_URL_TESTING}
ENV MONGODB_CONNECTION_STRING=${MONGODB_CONNECTION_STRING}
ENV APP_ENVIRONMENT=${APP_ENVIRONMENT}
ENV DEPLOY_TIMESTAMP=${DEPLOY_TIMESTAMP}
ENV JWT_PRIVATE_KEY=${JWT_PRIVATE_KEY}
ENV JWT_PUBLIC_KEY=${JWT_PUBLIC_KEY}
ENV GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
ENV APPLE_CLIENT_ID=${APPLE_CLIENT_ID}
ENV TEMPDROP_CLIENT_ID=${TEMPDROP_CLIENT_ID}
ENV TEMPDROP_CLIENT_SECRET=${TEMPDROP_CLIENT_SECRET}
ENV KLAVIYO_API_KEY=${KLAVIYO_API_KEY}

HEALTHCHECK --interval=30s --timeout=3s \
  CMD curl --fail http://localhost/health || exit 1
  
WORKDIR /usr/src/app

COPY package*.json ./

RUN npm install

COPY . .

RUN npm run build

EXPOSE 80

CMD [ "npm", "start" ]
