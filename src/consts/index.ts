export const Consts = {
  appName: 'kegg',
  apiVersion: '2.0.10',
  environment: process.env.APP_ENVIRONMENT || 'local',
  deployTimestamp: process.env.DEPLOY_TIMESTAMP || '',
  aws_access_key: process.env.AWS_ACCESS_KEY_ID || '',
  aws_secret_key: process.env.AWS_SECRET_ACCESS_KEY || '',
  aws_region: process.env.AWS_REGION || '',
  mixPanelToken: process.env.MIXPANEL_TOKEN || 'x',
  emailMeasurementsTo: ['<EMAIL>'],
  isDev: false,
  predictionUrl: process.env.LEGACY_PREDICTION_URL || 'http://************:7777',
  predictionUrlAndromeda: process.env.ANDROMEDA_PREDICTION_URL || 'localhost:5000',
  predictionUrlAndromedaTesting: process.env.ANDROMEDA_PREDICTION_URL_TESTING || 'localhost:5001',
  minimalAcceptedResistance: 0.0,
  maximalAcceptedResistance: 9000.0,
  dbConnectionString: process.env.MONGODB_CONNECTION_STRING || '',
  frontendUrl: process.env.FRONTEND_URL || 'https://static-content.kegg.tech/deeplink.html?token=',
  googleClientID: process.env.GOOGLE_CLIENT_ID || '740825853538-hpbji3l5126pfimg1tpuvpfirk35i8na.apps.googleusercontent.com,740825853538-9s875bd9qdekfu9odqk4n9cqerhineb6.apps.googleusercontent.com',
  appleClientID: process.env.APPLE_CLIENT_ID || 'com.ladytechnologies.kegg2.test',
  tempDropClientID: process.env.TEMPDROP_CLIENT_ID || '',
  tempDropClientSecret: process.env.TEMPDROP_CLIENT_SECRET || '',
  klaviyoAPIKey: process.env.KLAVIYO_API_KEY || '',
};
