import { Consts } from '@/consts';
import { IUser } from '@/types/user';
import Mixpanel from 'mixpanel';

export const sendToMixpanel = (user: IUser) => {
  let mixpanelApi: Mixpanel.Mixpanel | null = null;

  if (Consts.environment == 'live') {
    mixpanelApi = Mixpanel.init(Consts.mixPanelToken);
  }

  if (Consts.environment == 'live' && user.info != null && user.info.profile != null) {
    mixpanelApi?.people.set(user._id.toString(), {
      $created: user.created,
      cycle: user.info.profile.regularCycle ?? '',
      initialCycleLength: user.info.profile.initialCycleLength ?? '',
      trackingReason: user.info.profile.trackingReason ?? '',
      cycleCount: user.cycleCount ?? 1,
    });
  }
};
