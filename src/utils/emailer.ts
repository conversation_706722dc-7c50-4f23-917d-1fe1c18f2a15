import { Consts } from '@/consts';
import { SESClient, SendRawEmailCommand, SendRawEmailCommandOutput } from "@aws-sdk/client-ses";

// Create a new SES client with credentials and region from environment variables.
const sesClient = new SESClient({
  region: Consts.aws_region,
  credentials: {
    accessKeyId: Consts.aws_access_key,
    secretAccessKey: Consts.aws_secret_key,
  },
});

function send(
  emails: string[],
  subject: string,
  body: string,
  attachment: string | undefined,
  callback: (error: any, data?: SendRawEmailCommandOutput) => void
): void {
  const from = "<EMAIL>";

  let sesMail = `From: 'kegg App' <${from}>\n`;
  sesMail += `To: ${emails.join(", ")}\n`;
  sesMail += `Subject: ${subject}\n`;
  sesMail += "MIME-Version: 1.0\n";
  sesMail += 'Content-Type: multipart/mixed; boundary="NextPart"\n\n';
  sesMail += "--NextPart\n";
  sesMail += "Content-Type: text/html; charset=utf-8\n\n";
  sesMail += `${body}\n\n`;

  if (attachment) {
    sesMail += "--NextPart\n";
    sesMail += "Content-Type: text/plain;\n";
    sesMail += 'Content-Disposition: attachment; filename="measurement.txt"\n\n';
    sesMail += `${attachment}\n\n`;
    sesMail += "--NextPart--";
  }

  const params = {
    RawMessage: { Data: Buffer.from(sesMail) },
    Destinations: emails,
    Source: "'kegg' <" + from + ">'",
  };

  const command = new SendRawEmailCommand(params);

  sesClient
    .send(command)
    .then((data) => {
      callback(null, data);
    })
    .catch((error) => {
      console.error("Error sending email:", error);
      callback(error);
    });
}

export default { send } as const;