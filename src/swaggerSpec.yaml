openapi: 3.0.1
info:
  title: Kegg API
  version: 1.1.0
  description: API documentation for Kegg API
components:
  parameters:
    AccessToken:
      name: x-access-token
      in: header
      description: Access Token
      required: true
      schema:
        type: string
  schemas:
    UserDataV2:
      type: object
      properties:
        id:
          type: string
          description: The unique user identifier.
        email:
          type: string
          description: The user's email address.
        firstName:
          type: string
          description: The user's first name.
        lastName:
          type: string
          description: The user's last name.
        birthDate:
          type: string
          format: date
          description: The user's birth date in YYYY-MM-DD format.
        height:
          type: string
          description: The user's height (in metric units).
        weight:
          type: string
          description: The user's weight (in metric units).
        consent:
          type: object
          properties:
            research:
              type: boolean
            keggPlus:
              type: boolean
          description: Consent details.
        trackingReason:
          type: array
          items:
            type: string
          description: >-
            List of tracking reasons (e.g. getPregnant, trackMyCycle,
            exerciseMyPelvicFloor).
        cycleType:
          type: string
          enum:
            - regular
            - irregular
          description: The user's cycle type.
        initialCycleLength:
          type: string
          description: The initial cycle length.
        initialPeriodLength:
          type: string
          description: The initial period length.
        conditions:
          type: array
          items:
            type: string
          description: List of user conditions.
        lastUnfinishedCycleStart:
          type: string
          format: date-time
          description: Start date of the last unfinished cycle.
        medications:
          type: array
          items:
            type: string
          description: List of medications.
        emailVerified:
          type: boolean
          description: Whether the user's email is verified.
        keggIdentifiers:
          type: string
          description: KEGG device identifier.
paths:
  /calendar:
    post:
      summary: List calendar items
      description: >-
        Retrieve a list of calendar items based on the user's role, user ID,
        date range, pagination, and sorting options.
      tags:
        - Calendar
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Calendar item ID to filter by, if applicable.
                user:
                  type: string
                  description: User ID to filter calendar items by user.
                startDate:
                  type: string
                  format: date
                  description: Start date for filtering calendar items (YYYY-MM-DD).
                endDate:
                  type: string
                  format: date
                  description: End date for filtering calendar items (YYYY-MM-DD).
                pagesize:
                  type: integer
                  description: Number of items per page.
                page:
                  type: integer
                  description: Page number for pagination.
                sort:
                  type: integer
                  description: Sort order for date (-1 for descending, 1 for ascending).
      responses:
        '200':
          description: Success - List of calendar items.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        date:
                          type: string
                          example: '2024-11-11'
                        data:
                          type: object
                          description: Calendar data.
                        values:
                          type: array
                          description: List of values.
                          items:
                            type: string
                        lastMeasurementTime:
                          type: string
                          format: date-time
                          description: Last measurement timestamp.
        '400':
          description: Error - Bad Request.
        '500':
          description: Error - Server Error.
  /calendar/feed:
    post:
      summary: Retrieve calendar data with prediction and ovulation data
      description: >-
        This endpoint retrieves calendar data based on user role, token, and
        optional filtering by user ID or date range. The response includes both
        calendar items and prediction data for fertility and ovulation.
      tags:
        - Calendar
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Calendar item ID to filter by, if applicable.
                user:
                  type: string
                  description: User ID to filter calendar items by user.
      responses:
        '200':
          description: Success - Calendar data with predictions
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    additionalProperties:
                      type: object
                      properties:
                        date:
                          type: string
                          example: '2024-11-11'
                        prediction:
                          type: object
                          properties:
                            fertile:
                              type: boolean
                              example: true
                            ovulation:
                              type: boolean
                              example: true
                            period:
                              type: boolean
                              example: true
                        past:
                          type: object
                          properties:
                            ovulation:
                              type: boolean
                              example: true
                            beforeOvulation:
                              type: boolean
                              example: true
                            afterOvulation:
                              type: boolean
                              example: false
        '400':
          description: Error - Token missing or invalid request data
        '500':
          description: Error - Server Error
  /calendar/set:
    post:
      summary: Add or update a calendar entry
      description: >-
        This endpoint adds or updates a user's calendar entry based on the
        provided date. It can also create multiple days of data for a new user
        during onboarding.
      tags:
        - Calendar
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                date:
                  type: string
                  description: Date of the calendar entry in ISO format.
                type:
                  type: string
                  description: Type of calendar data.
                data:
                  type: object
                  description: Data object for the calendar entry.
                signupData:
                  type: object
                  description: Signup data for onboarding, if applicable.
                  properties:
                    keggIdentifier:
                      type: string
                      description: Unique identifier for the user.
                user:
                  type: string
                  description: (Optional) User ID if specifying a different user.
      responses:
        '200':
          description: Success - Calendar item added or updated
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Ok
                  item:
                    type: object
                    description: Calendar item details (returned only if updated).
                    properties:
                      date:
                        type: string
                        example: '2024-11-11'
                      data:
                        type: object
                        description: Data related to the calendar entry.
                      values:
                        type: array
                        items:
                          type: number
                        example:
                          - 37.5
                          - 37.6
                          - 37.4
        '400':
          description: Error - Missing or invalid request data
        '500':
          description: Error - Server Error
  /calendar/setPeriods:
    post:
      summary: Update period information for a user's calendar
      description: >-
        This endpoint updates the period information for multiple dates in the
        user's calendar.
      tags:
        - Calendar
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                periods:
                  type: object
                  description: >-
                    A map of dates to period indicators (1 for period, 0 for no
                    period).
                  example:
                    '2024-11-11': 1
                    '2024-11-12': 0
                  additionalProperties:
                    type: integer
                    enum:
                      - 0
                      - 1
                    description: 1 indicates a period day, 0 indicates no period.
      responses:
        '200':
          description: Success - Period data updated
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
        '400':
          description: Error - Missing or invalid request data
        '500':
          description: Error - Server Error
  /calendar/update:
    post:
      summary: Update calendar data for a specific date
      description: >-
        Updates the calendar data for a specific user and date. Only accessible
        by admin and support roles.
      tags:
        - Calendar
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user:
                  type: string
                  description: The ID of the user whose calendar data is being updated.
                  example: user12345
                date:
                  type: string
                  format: date
                  description: The date of the calendar entry to update (YYYY-MM-DD).
                  example: '2024-11-11'
                data:
                  type: object
                  description: The updated calendar data for the specified date.
                  example:
                    period: true
                    valueAtLogin: true
      responses:
        '200':
          description: Success - Calendar data updated
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Ok
        '400':
          description: Error - Missing or invalid request data
        '401':
          description: Error - Unauthorized access
        '404':
          description: Error - Calendar entry not found
        '500':
          description: Error - Server Error
  /calendar/remove:
    delete:
      summary: Remove a calendar entry for a specific date
      description: >-
        Deletes a specified calendar entry for a user by date. Only accessible
        by admin role.
      tags:
        - Calendar
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user:
                  type: string
                  description: The ID of the user whose calendar entry is being deleted.
                  example: user12345
                date:
                  type: string
                  format: date
                  description: The date of the calendar entry to remove (YYYY-MM-DD).
                  example: '2024-11-11'
      responses:
        '200':
          description: Success - Calendar entry removed
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Ok
        '400':
          description: Error - Missing or invalid request data
        '401':
          description: Error - Unauthorized access
        '404':
          description: Error - Calendar entry not found
        '500':
          description: Error - Server error
  /calendar/andromeda:
    post:
      summary: Retrieve cycle predictions using the Andromeda algorithm
      description: >
        This endpoint returns cycle predictions based on the authenticated
        user's token and role. For users with the roles `admin`, `support`, or
        `readonly`, an alternative user can be specified via the request body
        parameters `id` and `userId`. The number of cycles to predict can also
        be customized; if not provided or invalid, it defaults to 4.
      tags:
        - Calendar
      parameters:
        - in: header
          name: x-access-token
          description: Access token for authentication.
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                userId:
                  type: string
                  description: >
                    The user ID for which predictions are required. This is used
                    in conjunction with the `id` parameter for privileged users.
                cycles:
                  type: integer
                  description: Number of cycles for which predictions should be computed.
                  default: 4
                testing:
                  type: boolean
                  description: Flag to indicate if this is a test request.
                  default: false
              example:
                id: optional-id
                userId: user12345
                cycles: 10
                testing: false
      responses:
        '200':
          description: Successfully retrieved cycle predictions.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    description: Object containing the predicted cycle data.
        '400':
          description: Bad Request – Missing token or invalid input parameters.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Token is missing
        '500':
          description: >-
            Internal Server Error – An error occurred while processing the
            predictions.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Internal server error
  /calendar/feedV2:
    post:
      summary: Retrieve calendar data with updated fertility predictions
      description: >
        This endpoint retrieves calendar data using the new prediction engine.
        In addition to the standard calendar items (including past period data),
        extra days with non-zero fertility probabilities are added. For each
        day, the response includes the fertility probability and a flag
        indicating if the day is within the fertile window. (Note: period
        predictions from the new engine are not yet integrated; they are derived
        solely from past calendar data.)
      tags:
        - Calendar
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: Calendar item ID to filter by, if applicable.
                user:
                  type: string
                  description: User ID to filter calendar items by user.
                testing:
                  type: boolean
                  description: Flag to indicate if this is a test request.
                  default: false
      responses:
        '200':
          description: Success - Calendar data with new predictions
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  lastPeriodStartDay:
                    type: string
                    example: '2025-01-06'
                  nextPeriodStartDay:
                    type: string
                    nullable: true
                    example: null
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        date:
                          type: string
                          example: '2025-01-06'
                        lastMeasurementTime:
                          type: string
                          format: date-time
                          example: '2025-01-06T08:20:00Z'
                        data:
                          type: object
                          description: Calendar data such as period information.
                          example:
                            period: true
                            notes: User recorded period
                        values:
                          type: array
                          items:
                            type: number
                          example:
                            - 36.6
                        prediction:
                          type: object
                          properties:
                            fertilityProbability:
                              type: number
                              example: 0.28
                            ovulationProbability:
                              type: number
                              example: 0.12
                            fertile:
                              type: boolean
                              example: true
                            fertileWindow:
                              type: boolean
                              example: true
                            period:
                              type: boolean
                              example: true
                            ovulation:
                              type: boolean
                              example: false
        '400':
          description: Error - Token missing or invalid request data
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Token is missing
        '500':
          description: Error - Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Internal server error
  /cycle/:
    post:
      summary: Retrieve a list of calendar cycles
      description: >-
        Returns a list of calendar cycles filtered by user role and specified
        parameters. Admin and readonly roles can filter by user ID, start date,
        and end date. Regular users can retrieve only their own cycles.
      tags:
        - Calendar
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: ID of the cycle to retrieve (admin only).
                  example: cycle12345
                user:
                  type: string
                  description: The ID of the user whose cycles to retrieve (admin only).
                  example: user12345
                startDate:
                  type: string
                  format: date
                  description: Start date for filtering cycles (YYYY-MM-DD).
                  example: '2024-01-01'
                endDate:
                  type: string
                  format: date
                  description: End date for filtering cycles (YYYY-MM-DD).
                  example: '2024-12-31'
      responses:
        '200':
          description: Success - List of filtered cycles
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: string
                    userId:
                      type: string
                    startDate:
                      type: string
                      format: date
                    endDate:
                      type: string
                      format: date
                    data:
                      type: object
        '400':
          description: Error - Missing or invalid request data
        '401':
          description: Error - Unauthorized access
        '500':
          description: Error - Server error
  /cycle/withSuccess:
    post:
      summary: Retrieve cycle data with period information and measurements
      description: >-
        Retrieves a list of cycles, each containing period days and measurement
        data. Admin and readonly users can filter by user ID, start date, and
        end date, while regular users can only retrieve their own data.
      tags:
        - Cycle
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: ID of the specific cycle to retrieve (admin only).
                  example: cycle12345
                user:
                  type: string
                  description: User ID for cycle filtering (admin only).
                  example: user12345
                startDate:
                  type: string
                  format: date
                  description: Start date for filtering cycles (YYYY-MM-DD).
                  example: '2024-01-01'
                endDate:
                  type: string
                  format: date
                  description: End date for filtering cycles (YYYY-MM-DD).
                  example: '2024-12-31'
                sort:
                  type: boolean
                  description: Whether to sort the cycles in reverse order.
                  example: true
      responses:
        '200':
          description: Success - List of cycles with period and measurement data
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        startDate:
                          type: string
                          format: date
                          description: Start date of the cycle.
                        days:
                          type: array
                          items:
                            type: object
                            properties:
                              calendarId:
                                type: string
                                description: Calendar item ID.
                              day:
                                type: string
                                format: date
                                description: Day in the cycle.
                              val:
                                type: number
                                description: Last recorded value for the day.
                              lastMeasurementTime:
                                type: string
                                format: date-time
                                description: Timestamp of the last measurement.
                        length:
                          type: number
                          description: Duration of the cycle in days.
                        finished:
                          type: boolean
                          description: Whether the cycle is complete.
        '400':
          description: Error - Missing or invalid request data
        '401':
          description: Error - Unauthorized access
        '500':
          description: Error - Server error
  /cycle/remove:
    post:
      summary: Remove period markings from calendar records within a date range
      description: >-
        Allows admin users to remove period data from calendar records for a
        specified user and date range.
      tags:
        - Cycle
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user:
                  type: string
                  description: ID of the user whose calendar records should be updated.
                  example: user12345
                startDate:
                  type: string
                  format: date
                  description: Start date for the date range (YYYY-MM-DD).
                  example: '2024-01-01'
                endDate:
                  type: string
                  format: date
                  description: End date for the date range (YYYY-MM-DD).
                  example: '2024-01-31'
      responses:
        '200':
          description: Success - Period markings removed from calendar records
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                    example: Ok
        '400':
          description: Error - Missing or invalid request data
        '401':
          description: Error - Unauthorized access
        '404':
          description: Error - Calendar records not found
        '500':
          description: Error - Server error
  /externalConnection:
    post:
      summary: Retrieve external connections for the authenticated user
      description: >-
        Retrieves a list of external connections associated with the
        authenticated user.
      tags:
        - ExternalConnection
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      responses:
        '200':
          description: Success - External connections retrieved
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Ok
                  connections:
                    type: array
                    items:
                      type: object
                      properties:
                        _id:
                          type: string
                          description: Connection ID
                          example: connection12345
                        user:
                          type: string
                          description: ID of the associated user
                          example: user12345
                        provider:
                          type: string
                          description: The external provider name
                          example: provider_name
                        token:
                          type: string
                          description: >-
                            Access token for the external provider (sensitive
                            data)
                          example: some_access_token
                        createdAt:
                          type: string
                          format: date-time
                          description: Date when the connection was created
                          example: '2024-01-01T12:00:00Z'
        '401':
          description: Error - Unauthorized access
        '500':
          description: Error - Server error
  /externalConnection/create:
    post:
      summary: Create a new external connection
      description: >-
        Creates a new external connection for the authenticated user and removes
        any existing connections of the same type.
      tags:
        - ExternalConnection
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                type:
                  type: string
                  description: Type of the external connection (e.g., "tempdrop").
                  example: tempdrop
                token:
                  type: string
                  description: Authorization token received from the external provider.
                  example: authorization_code_123
              required:
                - type
                - token
      responses:
        '200':
          description: Success - External connection created
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Ok
                  connection:
                    type: object
                    properties:
                      _id:
                        type: string
                        description: Connection ID
                        example: connection12345
                      user:
                        type: string
                        description: User ID
                        example: user12345
                      type:
                        type: string
                        description: Type of the connection
                        example: tempdrop
                      created:
                        type: string
                        format: date-time
                        description: Date when the connection was created
                        example: '2024-01-01T12:00:00Z'
                      lastSynchronization:
                        type: string
                        format: date-time
                        nullable: true
                        description: >-
                          Last synchronization date (null if not synchronized
                          yet)
                        example: null
        '400':
          description: Error - Missing fields in the request body
        '401':
          description: Error - Unauthorized access
        '500':
          description: Error - Server error
  /externalConnection/synchronize:
    post:
      summary: Synchronize external connection data
      description: >-
        Fetches and synchronizes data from an external source for a specified
        connection.
      tags:
        - ExternalConnection
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: The ID of the external connection to synchronize.
                  example: connection12345
                unit:
                  type: string
                  description: >-
                    Unit of temperature, either 'C' for Celsius or 'F' for
                    Fahrenheit.
                  example: C
              required:
                - id
      responses:
        '200':
          description: Synchronization successful - returns synchronized items.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Ok
                  items:
                    type: array
                    items:
                      type: object
                      properties:
                        date:
                          type: string
                          description: The date of the measurement in YYYY-MM-DD format.
                          example: '2024-11-10'
                        data:
                          type: object
                          description: >-
                            Data associated with the measurement, including
                            temperature.
                          properties:
                            temperature:
                              type: object
                              properties:
                                value:
                                  type: number
                                  example: 36.7
                                source:
                                  type: string
                                  example: tempdrop
                        values:
                          type: array
                          items:
                            type: number
                          description: Historical values for the date.
                          example:
                            - 36.5
                            - 36.6
                        lastMeasurementTime:
                          type: string
                          format: date-time
                          description: Timestamp of the last measurement.
                          example: '2024-11-10T08:30:00Z'
        '400':
          description: Error - Missing required fields.
        '401':
          description: Error - Unauthorized access.
        '500':
          description: Error - Server error during synchronization.
  /externalConnection/synchornize:
    post:
      summary: Synchronize external connection data
      description: >-
        Fetches and synchronizes data from an external source for a specified
        connection.
      tags:
        - ExternalConnection
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: The ID of the external connection to synchronize.
                  example: connection12345
                unit:
                  type: string
                  description: >-
                    Unit of temperature, either 'C' for Celsius or 'F' for
                    Fahrenheit.
                  example: C
              required:
                - id
      responses:
        '200':
          description: Synchronization successful - returns synchronized items.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Ok
                  items:
                    type: array
                    items:
                      type: object
                      properties:
                        date:
                          type: string
                          description: The date of the measurement in YYYY-MM-DD format.
                          example: '2024-11-10'
                        data:
                          type: object
                          description: >-
                            Data associated with the measurement, including
                            temperature.
                          properties:
                            temperature:
                              type: object
                              properties:
                                value:
                                  type: number
                                  example: 36.7
                                source:
                                  type: string
                                  example: tempdrop
                        values:
                          type: array
                          items:
                            type: number
                          description: Historical values for the date.
                          example:
                            - 36.5
                            - 36.6
                        lastMeasurementTime:
                          type: string
                          format: date-time
                          description: Timestamp of the last measurement.
                          example: '2024-11-10T08:30:00Z'
        '400':
          description: Error - Missing required fields.
        '401':
          description: Error - Unauthorized access.
        '500':
          description: Error - Server error during synchronization.
  /externalConnection/remove:
    post:
      summary: Remove an external connection
      description: Deletes a specified external connection for the authenticated user.
      tags:
        - ExternalConnection
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: The ID of the external connection to be removed.
                  example: connection12345
              required:
                - id
      responses:
        '200':
          description: Successfully removed the external connection.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Ok
        '400':
          description: Missing required fields in the request.
        '404':
          description: External connection does not exist.
        '500':
          description: Error - Server error during removal.
  /health:
    get:
      summary: Health check endpoint
      description: Returns a simple "HEALTHY" message to indicate the server is running.
      tags:
        - Health
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      responses:
        '200':
          description: Server is healthy.
          content:
            text/plain:
              schema:
                type: string
                example: HEALTHY
  /:
    get:
      summary: Get basic application info
      description: Returns app name, API version, environment, and deployment timestamp.
      tags:
        - Index
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      responses:
        '200':
          description: Basic application info.
          content:
            text/plain:
              schema:
                type: string
                example: AppName v1.0.0 production 2024-11-11T12:00:00Z
  /appVersionRequirement:
    get:
      summary: Get app version requirements
      description: Returns the minimum and recommended app versions.
      tags:
        - Index
      responses:
        '200':
          description: App version requirements.
          content:
            application/json:
              schema:
                type: object
                properties:
                  minimumAppVersion:
                    type: string
                    example: '1.0'
                  recommendedVersion:
                    type: string
                    example: '1.0'
  /keggtest:
    post:
      summary: List Kegg tests with pagination and sorting
      description: Returns a paginated list of Kegg test records.
      tags:
        - KeggTest
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                page:
                  type: integer
                  example: 0
                pagesize:
                  type: integer
                  example: 20
                sortBy:
                  type: string
                  example: date
                sort:
                  type: integer
                  example: -1
      responses:
        '200':
          description: List of Kegg test records with pagination.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  pagination:
                    type: object
                    properties:
                      page:
                        type: integer
                      pagesize:
                        type: integer
                      total:
                        type: integer
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        date:
                          type: string
                          format: date-time
                        success:
                          type: boolean
                        serial:
                          type: string
                        ip:
                          type: string
                        result:
                          type: string
  /keggtest/add:
    post:
      summary: Add or update a Kegg test record
      description: Adds a new Kegg test or updates an existing one by serial.
      tags:
        - KeggTest
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                serial:
                  type: string
                  example: AB123
                success:
                  type: boolean
                  example: true
                result:
                  type: string
                  example: Sample result
      responses:
        '200':
          description: Status of the add operation.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Ok
  /keggtest/export:
    post:
      summary: Export Kegg test data as CSV
      description: Exports Kegg test data in CSV format.
      tags:
        - KeggTest
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      responses:
        '200':
          description: CSV data exported successfully.
          content:
            text/csv:
              schema:
                type: string
                example: |-
                  ts;success;serial;ip;data
                  10/10/2024 12:00:00;true;AB123;127.0.0.1;Sample result
  /keggtest/getSerial:
    post:
      summary: Generate a new serial for Kegg testing
      description: Generates and returns a new serial for Kegg testing.
      tags:
        - KeggTest
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                password:
                  type: string
                  example: your_write_password
      responses:
        '200':
          description: New serial generated successfully.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Ok
                  data:
                    type: object
                    properties:
                      _id:
                        type: string
                        example: 605c67e7e55b4e20a8f23456
  /measurement:
    post:
      summary: List measurements with pagination
      description: >-
        Returns a paginated list of measurement records based on query
        parameters.
      tags:
        - Measurement
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                page:
                  type: integer
                  example: 0
                pagesize:
                  type: integer
                  example: 20
                query:
                  type: object
                  additionalProperties: true
                sortBy:
                  type: string
                  example: date
                sort:
                  type: integer
                  example: -1
      responses:
        '200':
          description: List of measurements with pagination.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Ok
                  pagination:
                    type: object
                    properties:
                      page:
                        type: integer
                      pagesize:
                        type: integer
                      total:
                        type: integer
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        _id:
                          type: string
                        keggId:
                          type: string
                        serverDate:
                          type: string
                          format: date-time
                        data:
                          type: array
                          items:
                            type: object
                        date:
                          type: string
                          format: date-time
                        protocolVersion:
                          type: string
                        measurementValue:
                          type: number
                        appVersion:
                          type: string
                        firmwareVersion:
                          type: string
                        phoneInfo:
                          type: object
                          properties:
                            platform:
                              type: string
                              example: Android
                            os:
                              type: string
                              example: '10.0'
                            brand:
                              type: string
                              example: Samsung
                            model:
                              type: string
                              example: Galaxy S10
                        user:
                          type: string
                          example: user123
                        keggInfos:
                          type: array
                          items:
                            type: object
                        sequenceInfo:
                          type: object
                        resultCode:
                          type: string
                          example: success
                        session:
                          type: array
                          items:
                            type: object
                        computedValues:
                          type: object
                        temperature:
                          type: number
                          example: 37
        '400':
          description: Bad request due to missing or invalid parameters.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: 'Error: Invalid query parameters'
  /measurement/add:
    post:
      summary: Add a new measurement
      description: >-
        Adds a new measurement record to the database and associates it with the
        user.
      tags:
        - Measurement
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - date
                - keggId
                - phoneInfo
                - sequenceInfo
                - resultCode
                - protocolVersion
                - appVersion
                - rawKeggInfos
                - data
                - session
              properties:
                date:
                  type: string
                  format: date-time
                keggId:
                  type: string
                phoneInfo:
                  type: object
                  properties:
                    platform:
                      type: string
                    os:
                      type: string
                    brand:
                      type: string
                    model:
                      type: string
                sequenceInfo:
                  type: object
                resultCode:
                  type: string
                  example: OK
                protocolVersion:
                  type: string
                appVersion:
                  type: string
                rawKeggInfos:
                  type: array
                  items:
                    type: object
                    properties:
                      date:
                        type: string
                        format: date-time
                      info:
                        type: string
                data:
                  type: array
                  items:
                    type: string
                    format: byte
                session:
                  type: object
      responses:
        '200':
          description: Measurement successfully saved.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Measurement saved
        '400':
          description: Bad request due to missing or invalid parameters.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Invalid input data
        '500':
          description: Server error while processing the measurement.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Measurement add error
  /measurement/update:
    post:
      summary: Update a measurement
      description: Updates an existing measurement in the database.
      tags:
        - Measurement
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
              properties:
                id:
                  type: string
                  description: The ID of the measurement to update.
                data:
                  type: array
                  items:
                    type: string
                    format: byte
                  description: The new measurement data.
      responses:
        '200':
          description: Measurement successfully updated.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Ok
        '400':
          description: Bad request due to missing or invalid parameters.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Measurement does not exist
        '401':
          description: Unauthorized access due to insufficient permissions.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Unauthorized
        '500':
          description: Server error while processing the measurement update.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Measurement update error
  /measurement/remove:
    post:
      summary: Remove a measurement
      description: >-
        Removes an existing measurement from the database, along with associated
        calendar entries and user data.
      tags:
        - Measurement
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
              properties:
                id:
                  type: string
                  description: The ID of the measurement to remove.
      responses:
        '200':
          description: Measurement successfully removed.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Ok
        '400':
          description: Bad request due to missing or invalid parameters.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Measurement does not exist
        '401':
          description: >-
            Unauthorized access due to insufficient permissions (only admin can
            remove measurements).
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Unauthorized
        '404':
          description: Calendar entry not found for the measurement.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Calendar does not exist
        '500':
          description: Server error while processing the measurement removal.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Measurement remove error
  /measurement/export:
    post:
      summary: Export measurements to CSV
      description: >-
        Exports measurement data to a CSV file for a given user or measurement
        ID.
      tags:
        - Measurement
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user:
                  type: string
                  description: The ID of the user whose measurements to export (optional).
                id:
                  type: string
                  description: The ID of the measurement to export (optional).
      responses:
        '200':
          description: CSV file with measurement data.
          content:
            text/csv:
              schema:
                type: string
                example: |
                  r,rp,rn,pm,nm id=12345
                  5.6,3.2,2.3,0.1,-0.1
                  7.8,4.2,3.1,0.2,-0.2
        '400':
          description: Bad request due to invalid parameters or missing user/ID.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Invalid request data
        '401':
          description: Unauthorized access due to insufficient permissions.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Unauthorized
        '500':
          description: Internal server error while generating or exporting the CSV file.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Measurement export error
  /measurement/export/aggregate:
    post:
      summary: Export aggregated measurement data to CSV
      description: >-
        Exports aggregated measurement data, including percentile values, for
        each measurement.
      tags:
        - Measurement
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                values:
                  type: array
                  items:
                    type: object
                    properties:
                      name:
                        type: string
                        description: The name of the measurement field (e.g., 'rp', 'rn').
                      percentile:
                        type: integer
                        description: >-
                          The percentile value to calculate (e.g., 10, 30, 50,
                          70, 90).
                  description: >-
                    An array of values specifying the name and percentile to
                    calculate. If not provided, defaults to the specified set of
                    values.
      responses:
        '200':
          description: CSV file with aggregated measurement data.
          content:
            text/csv:
              schema:
                type: string
                example: |
                  rp10,rp30,rp50,rp70,rp90,rn10,rn30,rn50,rn70,rn90,id
                  1.2,3.4,5.6,7.8,9.0,0.1,0.3,0.5,0.7,0.9,12345
                  2.3,4.5,6.7,8.9,10.1,1.2,1.4,1.6,1.8,2.0,67890
        '400':
          description: Bad request due to invalid parameters.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Invalid request data
        '401':
          description: Unauthorized access due to insufficient permissions.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Unauthorized
        '500':
          description: >-
            Internal server error while generating or exporting the aggregated
            CSV file.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Measurement export aggregate error
  /prediction:
    get:
      summary: Retrieve prediction data for a specific user
      description: >-
        Fetches prediction data based on a user's calendar entries and cycle
        history. Calculates average cycle length and provides the last period
        start date.
      tags:
        - Prediction
      parameters:
        - $ref: '#/components/parameters/AccessToken'
        - in: query
          name: user
          required: true
          schema:
            type: string
          description: User ID for which prediction data is requested.
      responses:
        '200':
          description: Prediction data successfully retrieved.
          content:
            application/json:
              schema:
                type: object
                properties:
                  average_cycle_length:
                    type: integer
                    example: 28
                  start_of_last_period:
                    type: string
                    format: date
                    example: '2024-10-01'
                  data:
                    type: array
                    description: Measurement data related to the prediction (if available).
                    items:
                      type: object
        '401':
          description: Unauthorized access due to missing or invalid credentials.
        '422':
          description: >-
            Unprocessable Entity - either due to lack of sufficient cycle data
            or a server error.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Not enough cycle data
  /prediction/healthcheck:
    get:
      summary: Perform a health check on the prediction service
      description: >-
        Calls an external health check endpoint to confirm if the prediction
        service is operational.
      tags:
        - Prediction
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      responses:
        '200':
          description: Health check performed successfully.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
        '500':
          description: Internal server error during health check.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Prediction healthcheck error
  /statistics:
    post:
      summary: Retrieve user statistics
      description: >-
        Fetches a set of statistical data on users based on device platform, age
        range, tracking purpose, and engagement metrics.
      tags:
        - Statistics
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      responses:
        '200':
          description: Successfully retrieved user statistics.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      totalRegistered:
                        type: integer
                        example: 1000
                      usersByPlatform:
                        type: object
                        properties:
                          ios:
                            type: integer
                            example: 600
                          android:
                            type: integer
                            example: 300
                          others:
                            type: integer
                            example: 100
                      usersByAge:
                        type: object
                        properties:
                          users_18_24:
                            type: integer
                            example: 200
                          users_25_34:
                            type: integer
                            example: 300
                          users_35_44:
                            type: integer
                            example: 150
                          users_45_54:
                            type: integer
                            example: 100
                          users_55_64:
                            type: integer
                            example: 50
                          users_65:
                            type: integer
                            example: 20
                      usersByPurpose:
                        type: object
                        properties:
                          trying_to_conceive:
                            type: integer
                            example: 400
                          cycle_info_tracking_fertility:
                            type: integer
                            example: 300
                          not_sure_yet:
                            type: integer
                            example: 100
                      totalWithMoreThanOneMeasurement:
                        type: integer
                        example: 200
                      totalWithMoreThanTwoExercises:
                        type: integer
                        example: 150
        '401':
          description: Unauthorized access due to insufficient permissions.
        '500':
          description: Internal server error during data retrieval.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: 'Stats error: {error message}'
  /user:
    post:
      summary: List users with various filters
      description: >-
        Fetches a paginated list of users with optional filters such as email,
        name, tracking reason, pregnancy status, app version, firmware, and
        serial. Only authorized roles (admin, support, readonly) can apply
        filters; other users can only query their own data.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: ID of a specific user.
                email:
                  type: string
                  description: Email filter for the user search.
                name:
                  type: string
                  description: Name filter for first or last name.
                trackingReason:
                  type: string
                  description: User's tracking reason.
                pregnancy:
                  type: boolean
                  description: Filter for users with pregnancy status.
                appVersion:
                  type: string
                  description: Filter by app version.
                firmware:
                  type: string
                  description: Filter by firmware version.
                serial:
                  type: string
                  description: Filter by Kegg device serial.
                page:
                  type: integer
                  description: Page number for pagination.
                  default: 0
                pagesize:
                  type: integer
                  description: Number of users per page.
                  default: 20
                sortBy:
                  type: string
                  description: Field to sort the results by.
                  default: created
                sort:
                  type: integer
                  description: Sort order, 1 for ascending and -1 for descending.
                  default: -1
      responses:
        '200':
          description: Successfully retrieved user list.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Ok
                  pagination:
                    type: object
                    properties:
                      page:
                        type: integer
                        example: 0
                      pagesize:
                        type: integer
                        example: 20
                      total:
                        type: integer
                        example: 1000
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        _id:
                          type: string
                          description: User ID.
                        email:
                          type: string
                          description: User's email.
                        phone:
                          type: string
                          description: User's phone number.
                        group:
                          type: string
                          description: User's group.
                        measurementsCount:
                          type: integer
                          description: Count of measurements for the user.
                        calendarCount:
                          type: integer
                          description: Count of calendar entries for the user.
                        enabled:
                          type: boolean
                        test:
                          type: boolean
                        created:
                          type: string
                          format: date-time
                        lastActivity:
                          type: string
                          format: date-time
                        role:
                          type: string
                        info:
                          type: object
                        cycleCount:
                          type: integer
        '400':
          description: Missing token or unauthorized access.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Token is missing
        '500':
          description: Server error while fetching user list.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: 'User list fail: [Error details]'
  /user/signup:
    post:
      summary: Sign up a new user
      description: >-
        Creates a new user with the provided email and password. Optionally,
        sets email verification status and user language.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  description: User's email address.
                password:
                  type: string
                  format: password
                  description: User's password.
                language:
                  type: string
                  description: Preferred language of the user.
                needToVerifyEmail:
                  type: boolean
                  description: Indicates if the user needs to verify their email.
      responses:
        '200':
          description: Successfully created a new user.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  token:
                    type: string
                    description: Authentication token for the new user.
                  message:
                    type: string
                    example: Signup successful
                  user:
                    type: object
                    properties:
                      _id:
                        type: string
                        description: User ID of the newly created user.
                      email:
                        type: string
                        description: User's email address.
                      info:
                        type: object
                        description: Additional information about the user.
        '400':
          description: Missing fields in the signup request.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Missing fields
        '409':
          description: Email already exists.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Email already exists
                  errorCode:
                    type: integer
                    example: 1
        '500':
          description: Internal server error during user creation.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: 'User create error: [Error details]'
  /user/login:
    post:
      summary: Log in a user
      description: >-
        Authenticates a user with email and password. For admin, support, or
        readonly roles, an SMS token may be required.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  description: User's email address.
                password:
                  type: string
                  format: password
                  description: User's password.
                token:
                  type: string
                  description: SMS token for users in admin, support, or readonly roles.
      responses:
        '200':
          description: Successfully authenticated the user.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  token:
                    type: string
                    description: Authentication token for the user.
                  message:
                    type: string
                    example: Authentication successful
                  user:
                    type: object
                    properties:
                      _id:
                        type: string
                        description: User ID of the authenticated user.
                      email:
                        type: string
                        description: User's email address.
                      info:
                        type: object
                        description: Additional information about the user.
        '400':
          description: Missing fields in the login request.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Missing fields
        '401':
          description: Authentication failed due to invalid credentials or token.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Invalid token
        '403':
          description: Account disabled, preventing login.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Account disabled
        '500':
          description: Internal server error during authentication.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Could not authenticate
  /user/verify:
    post:
      summary: Verify a user's token and update their information
      description: >-
        Verifies the provided token, updates user activity, and optionally
        updates language, app version, device info, and firmware version.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                language:
                  type: string
                  description: Language preference of the user.
                appVersion:
                  type: string
                  description: Version of the app the user is using.
                deviceInfo:
                  type: string
                  description: Information about the user's device.
                lastKeggFirmwareVersion:
                  type: string
                  description: Last known firmware version of the user's device.
      responses:
        '200':
          description: Successfully verified and updated user information.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Ok
                  info:
                    type: object
                    description: Updated user information.
                    properties:
                      language:
                        type: string
                        example: en
                      appVersion:
                        type: string
                        example: 1.2.3
                      deviceInfo:
                        type: string
                        example: iPhone 12
                      lastKeggFirmwareVersion:
                        type: string
                        example: 1.0.1
                  userId:
                    type: string
                    description: The ID of the verified user.
                    example: 507f191e810c19729de860ea
                  onboard:
                    type: boolean
                    description: Indicates if the user has completed onboarding.
                    example: true
                  created:
                    type: string
                    format: date-time
                    description: Date and time the user was created.
                    example: '2023-01-01T00:00:00.000Z'
        '400':
          description: Missing or invalid token.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Token is missing
        '403':
          description: Account is disabled, preventing verification.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Account disabled
        '500':
          description: Internal server error during verification.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Error message
  /user/create:
    post:
      summary: Create a new user
      description: >-
        Creates a new user with the provided details if authenticated by a valid
        token.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
                - role
              properties:
                email:
                  type: string
                  description: Email of the new user.
                password:
                  type: string
                  description: Password for the new user.
                role:
                  type: string
                  enum:
                    - admin
                    - support
                    - readonly
                    - user
                  description: Role of the new user.
                enabled:
                  type: boolean
                  description: Status of the user's account.
                test:
                  type: boolean
                  description: Indicates if the account is for testing.
                phone:
                  type: string
                  description: Phone number of the user.
                group:
                  type: string
                  description: Group the user belongs to.
                firstName:
                  type: string
                  description: First name of the user.
                lastName:
                  type: string
                  description: Last name of the user.
                note:
                  type: string
                  description: Additional note for the user.
                keggIdentifiers:
                  type: string
                  description: KEGG device identifiers.
                lastUnfinishedCycleStart:
                  type: string
                  format: date-time
                  description: Start date of the last unfinished cycle.
                lastFinishedCycleLength:
                  type: number
                  description: Length of the last completed cycle.
                initialCycleLength:
                  oneOf:
                    - type: number
                    - type: string
                      enum:
                        - not_sure
                  description: Initial cycle length or 'not_sure' if unknown.
      responses:
        '200':
          description: Successfully created the user.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Ok
                  data:
                    type: object
                    description: Details of the created user.
        '400':
          description: Missing required fields for user creation.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Missing fields:<br>email, password
        '401':
          description: Invalid or missing token.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Token is missing
        '500':
          description: Internal server error during user creation.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Error message
  /user/update:
    post:
      summary: Update an existing user
      description: >-
        Updates user information if authenticated with a valid token. Admins and
        support roles can update any user by ID; others can only update their
        own profile.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: >-
                    ID of the user to update. Required for admin and support
                    roles; ignored for regular users.
                email:
                  type: string
                  description: New email for the user.
                password:
                  type: string
                  description: New password for the user.
                role:
                  type: string
                  enum:
                    - admin
                    - support
                    - readonly
                    - user
                  description: >-
                    New role for the user. Only admins and support roles can set
                    this.
                enabled:
                  type: boolean
                  description: Account enabled status.
                phone:
                  type: string
                  description: Phone number of the user.
                group:
                  type: string
                  description: Group the user belongs to.
                firstName:
                  type: string
                  description: First name of the user.
                lastName:
                  type: string
                  description: Last name of the user.
                note:
                  type: string
                  description: Note for the user.
                keggIdentifiers:
                  type: string
                  description: KEGG device identifiers.
                lastUnfinishedCycleStart:
                  type: string
                  format: date-time
                  description: Start date of the last unfinished cycle.
                lastFinishedCycleLength:
                  type: number
                  description: Length of the last completed cycle.
                initialCycleLength:
                  oneOf:
                    - type: number
                    - type: string
                      enum:
                        - not_sure
                  description: Initial cycle length or 'not_sure' if unknown.
                trackingReason:
                  type: string
                  enum:
                    - trying_to_conceive
                    - cycle_info_tracking_fertility
                    - not_sure_yet
                  description: User's tracking reason.
                regularCycle:
                  oneOf:
                    - type: boolean
                    - type: string
                      enum:
                        - not_specified
                  description: Whether the user has a regular cycle.
                tryingToConceiveDuration:
                  type: string
                  description: Duration of trying to conceive.
                height:
                  type: number
                  description: Height of the user.
                weight:
                  type: number
                  description: Weight of the user.
                supplements:
                  type: array
                  items:
                    type: string
                  description: List of supplements.
                consent:
                  type: object
                  properties:
                    research:
                      type: boolean
                      description: Consent for research purposes.
                    keggPlus:
                      type: boolean
                    description: Consent for Kegg Plus.
      responses:
        '200':
          description: Successfully updated the user profile.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Ok
                  profile:
                    type: object
                    description: Updated profile details of the user.
        '400':
          description: User does not exist.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: User does not exist
        '401':
          description: Invalid or missing token.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Token is missing
        '500':
          description: Internal server error during user update.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Error message
  /user/changePassword:
    post:
      summary: Change the user password
      description: >-
        Updates the password for the authenticated user. Requires the user's
        current access token.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                password:
                  type: string
                  description: New password for the user.
                  example: newPassword123
              required:
                - password
      responses:
        '200':
          description: Successfully changed the user password.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Changed
        '400':
          description: Missing fields, including the new password.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Missing fields
        '401':
          description: Invalid or missing token.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Token is missing
        '500':
          description: Internal server error during password change.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Error message
  /user/remove:
    post:
      summary: Remove a user and associated data
      description: >-
        Deletes a user along with all associated measurements, calendar entries,
        and other data. Requires the user's access token.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: The ID of the user to be removed (optional for admin users).
                  example: 12345abcde
              required:
                - id
      responses:
        '200':
          description: Successfully removed the user and their associated data.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Deleted
        '400':
          description: Missing required token or ID.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Token is missing
        '401':
          description: >-
            Unauthorized request if the user is not an admin and the ID is not
            their own.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Unauthorized
        '500':
          description: Internal server error during the removal process.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Error message
  /user/copy:
    post:
      summary: Copy a user
      description: >-
        Copies a user's data (measurements, calendar, etc.) and creates a new
        user with the provided email, password, and other information. This
        operation requires an admin role.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                  description: The ID of the user to copy.
                  example: 60c72b2f9b1e8e31a1e58767
                email:
                  type: string
                  description: The email for the new user.
                  example: <EMAIL>
                password:
                  type: string
                  description: The password for the new user.
                  example: newuserpassword
                firstName:
                  type: string
                  description: The first name of the new user.
                  example: John
                lastName:
                  type: string
                  description: The last name of the new user.
                  example: Doe
              required:
                - id
                - email
                - password
                - firstName
                - lastName
      responses:
        '200':
          description: Successfully copied the user and associated data.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: User copied
                  data:
                    type: object
                    description: The newly created user.
        '400':
          description: Missing required fields or invalid data.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Missing fields
        '401':
          description: Unauthorized request (only admins can copy users).
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Unauthorized
        '409':
          description: Email already exists.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Email already exists
        '500':
          description: Internal server error during the copy process.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Error message
  /user/export:
    post:
      summary: Export user data to CSV
      description: >-
        Exports a list of user data based on selected columns to a CSV format.
        Only accessible by users with the roles of 'admin', 'support', or
        'readonly'.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                columns:
                  type: array
                  items:
                    type: string
                  description: >
                    List of columns to be exported. The list can include any of
                    the following: 'id', 'email', 'firstName', 'lastName',
                    'serial', 'created', 'lastActivity', 'measurements',
                    'cycles', 'diary', and others related to the user's profile,
                    calendar, and device information.
                  example:
                    - id
                    - email
                    - firstName
                    - lastName
                    - dateOfBirth
      responses:
        '200':
          description: Successfully exported user data as CSV.
          content:
            text/csv:
              schema:
                type: string
                example: >
                  id,email,firstName,lastName,created,lastActivity

                  12345,<EMAIL>,John,Doe,2022-01-01 12:00:00,2023-11-12
                  15:30:00
        '400':
          description: Missing required fields or invalid columns specified.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Missing columns
        '401':
          description: Unauthorized access due to insufficient role permissions.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Unauthorized
        '500':
          description: Internal server error during the export process.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Error during export
  /user/sendToken:
    post:
      summary: Send a verification token to the user
      description: >-
        Authenticates the user with email and password, then sends a
        verification token via SMS if the user has a phone number. If no phone
        number is available, it proceeds with a standard login.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  description: The user's email address.
                password:
                  type: string
                  description: The user's password.
              required:
                - email
                - password
              example:
                email: <EMAIL>
                password: password123
      responses:
        '200':
          description: >-
            Successfully authenticated, token sent by SMS if phone number is
            available.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
        '400':
          description: Missing email or password in the request.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Missing fields
        '401':
          description: Authentication failed due to invalid email or password.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Could not authenticate
        '500':
          description: Internal server error during the process.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Could not authenticate
  /user/verifyEmail:
    post:
      summary: Verify the user's email status
      description: >-
        Checks if the user's email is verified. Responds with success or failure
        depending on the email verification status.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      responses:
        '200':
          description: Successfully verified the user's email status.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Ok
        '400':
          description: Token is missing or invalid, or the email is not verified.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example:
                      - Token is missing
                      - Email not verified
        '500':
          description: Internal server error during the email verification process.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Error
  /user/confirmEmail:
    get:
      summary: Confirm a user's email
      description: >-
        Confirms the email address of the user by setting the `emailVerified`
        status to `true`. Requires the email as a query parameter.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      responses:
        '200':
          description: Successfully confirmed the user's email.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Ok
        '400':
          description: Missing email query parameter.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Missing fields
        '404':
          description: User with the given email does not exist.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: User does not exist
        '500':
          description: Internal server error while processing the email confirmation.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Error
  /user/predictions:
    post:
      summary: Get ovulation confirmation and predictions for a user
      description: >-
        Sends requests to an external service to get ovulation confirmation and
        predictions for a specific user, and returns the results. Requires the
        user ID and a valid authentication token.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user:
                  type: string
                  description: The ID of the user whose predictions are needed.
                  example: user123
      responses:
        '200':
          description: Successfully fetched predictions and ovulation confirmation.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  confirmation:
                    type: object
                    description: >-
                      The confirmation data returned by the external ovulation
                      service.
                  prediction:
                    type: object
                    description: >-
                      The prediction data returned by the external prediction
                      service.
        '400':
          description: Missing token or user ID in the request.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Token is missing
        '401':
          description: Unauthorized access due to insufficient user role.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Unauthorized
        '500':
          description: >-
            Internal server error while fetching predictions or confirmation
            data.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Error
  /user/sendResetPasswordLink:
    post:
      summary: Send a password reset link to the user's email
      description: >-
        Sends a password reset link to the user's email if the provided email
        exists in the system. The link will be valid for 1 hour.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  description: The user's email address.
                  example: <EMAIL>
      responses:
        '200':
          description: A reset password link was successfully sent to the user's email.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
        '400':
          description: Missing email in the request body.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Missing fields
        '404':
          description: The provided email does not exist in the system.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
        '500':
          description: Internal server error while trying to send the reset password email.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Error
  /user/resetPassword:
    get:
      summary: Serve the password reset form
      description: >-
        Renders a password reset form where the user can enter a new password.
        Requires the `email` and `token` as query parameters.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
        - in: query
          name: email
          required: true
          description: The user's email address for password reset.
          schema:
            type: string
            format: email
            example: <EMAIL>
        - in: query
          name: token
          required: true
          description: The temporary token sent to the user's email for password reset.
          schema:
            type: string
            example: abcdef123456
      responses:
        '200':
          description: Successfully served the password reset form.
          content:
            text/html:
              schema:
                type: string
                example: <html><body><form>...</form></body></html>
        '400':
          description: Missing `email` or `token` in the query parameters.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Missing fields
    post:
      summary: Reset the user's password
      description: >-
        Resets the user's password if the provided `email`, `token`, and
        `password` are valid. The token should be within the validity period.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  description: The user's email address.
                  example: <EMAIL>
                token:
                  type: string
                  description: >-
                    The temporary token sent to the user's email for password
                    reset.
                  example: abcdef123456
                password:
                  type: string
                  description: The new password to set for the user.
                  example: newpassword123
      responses:
        '200':
          description: Successfully changed the user's password.
          content:
            text/html:
              schema:
                type: string
                example: <html><body>Password changed successfully.</body></html>
        '400':
          description: Missing fields or invalid password.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Missing fields or invalid password.
        '404':
          description: User not found or token expired.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: User not found or token expired.
        '500':
          description: Internal server error.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Could not change password
  /user/test:
    get:
      summary: Retrieve combined private and public key content
      description: >-
        Reads private and public key files from the token directory and returns
        the combined content as HTML.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      responses:
        '200':
          description: Successfully retrieved key content in HTML.
          content:
            text/html:
              schema:
                type: string
                example: <html>...</html>
        '500':
          description: Error reading key files or generating HTML content.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Error message explaining the failure
  /user/test2:
    get:
      summary: Retrieve constant values as HTML
      description: >-
        Returns constant values in HTML format using a JSON stringified
        representation.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      responses:
        '200':
          description: Successfully retrieved constant values in HTML format.
          content:
            text/html:
              schema:
                type: string
                example: <html>...</html>
        '500':
          description: Error generating HTML content from constants.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Error message explaining the failure
  /user/requestLoginLink:
    post:
      summary: Request a magic login link via email
      description: >
        Generates and sends a magic login link to the specified email address.
        The link is valid for 1 hour. For Apple App Store review testing, using
        the email "<EMAIL>" will return a token
        directly without sending an email, allowing immediate authentication for
        testing purposes.
      tags:
        - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  description: >
                    The user's email address to send the login link. Special
                    case: Using "<EMAIL>" will return a
                    token directly for App Store review testing.
                  example: <EMAIL>
      responses:
        '200':
          description: Successfully sent the login link or returned a token for testing.
          content:
            application/json:
              schema:
                oneOf:
                  - type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      message:
                        type: string
                        example: Login link sent to your email
                  - type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      storeReviewTesting:
                        type: boolean
                        example: true
                      token:
                        type: string
                        example: eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
  /user/signupV2:
    post:
      summary: Sign up a new user (with magic link or Apple/Google SSO)
      description: >-
        Creates a new user using either email (magic link) or external
        authorization methods like Google or Apple.
      tags:
        - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                authorizationType:
                  type: string
                  enum:
                    - email
                    - google
                    - apple
                  description: The authorization type for signup.
                identityToken:
                  type: string
                  description: >-
                    Either the user's email (for email login) or an identity
                    token for SSO.
                language:
                  type: string
                  description: Preferred language of the user.
              required:
                - authorizationType
                - identityToken
      responses:
        '200':
          description: Successfully signed up the user.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Check your email for login link
                  token:
                    type: string
                    description: The authentication token for the new user.
                  user:
                    type: object
                    description: User details.
                  freshCreated:
                    type: boolean
                    description: Indicates if the user was freshly created.
        '400':
          description: Missing required fields or invalid input.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Missing fields
        '409':
          description: User already exists.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: User already exists
        '500':
          description: Internal server error during signup.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Authentication error
  /user/loginV2:
    post:
      summary: Log in a user (with magic link or Apple/Google SSO)
      description: >-
        Authenticates a user through either email-based login link or external
        services like Google and Apple.
      tags:
        - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                authorizationType:
                  type: string
                  enum:
                    - email
                    - google
                    - apple
                  description: The type of login.
                identityToken:
                  type: string
                  description: >-
                    The token for authentication (magic link token or SSO
                    identity token).
                language:
                  type: string
                  description: Preferred language of the user.
              required:
                - authorizationType
                - identityToken
      responses:
        '200':
          description: Successfully logged in the user.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Logged in
                  token:
                    type: string
                    description: Authentication token for the user.
                  user:
                    type: object
                    description: User details.
                  freshCreated:
                    type: boolean
                    description: Indicates if the user was freshly created.
        '400':
          description: Invalid or expired token.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Invalid or expired token
        '401':
          description: Authentication failed.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Authentication failed
        '500':
          description: Server error during login.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Authentication error
  /user/getUserV2:
    post:
      summary: Retrieve user data in transitional format (V2)
      description: >-
        Retrieves a user profile in the new transitional format for migration
        purposes. Requires a valid access token.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      responses:
        '200':
          description: Successfully retrieved user transitional data.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: User retrieved successfully
                  data:
                    $ref: '#/components/schemas/UserDataV2'
        '400':
          description: Missing or invalid token.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Token is missing
        '500':
          description: Server error retrieving user data.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Server error
  /user/updateUserV2:
    post:
      summary: Update user data in transitional format (V2)
      description: >-
        Updates a user profile in the new transitional format for migration
        purposes. Admins and support roles can update any user; others can
        update only their own profile. Requires a valid access token.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserDataV2'
      responses:
        '200':
          description: Successfully updated user transitional data.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: User updated successfully
                  data:
                    $ref: '#/components/schemas/UserDataV2'
        '400':
          description: Missing or invalid token or user not found.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Token is missing
        '500':
          description: Server error updating user data.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Server error
  /user/klaviyoEvent:
    post:
      summary: Send a Klaviyo event
      description: >-
        Sends a custom event to Klaviyo with the specified action code,
        human-friendly name, and custom user properties.
      tags:
        - Users
      parameters:
        - $ref: '#/components/parameters/AccessToken'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                eventAction:
                  type: string
                  description: The action code associated with the event.
                  example: something-happened
                eventName:
                  type: string
                  description: The human-friendly name of the event.
                  example: Something happened with this User
                userProperties:
                  type: object
                  additionalProperties:
                    type: string
                  description: A key-value object defining custom properties on Klaviyo.
                  example:
                    email: <EMAIL>
                    plan: premium
      responses:
        '200':
          description: Successfully sent the Klaviyo event.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Event sent successfully
        '400':
          description: Missing or invalid fields in the request.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Missing fields
        '401':
          description: Missing or invalid token.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Token is missing
        '500':
          description: Internal server error while sending the event.
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Error sending event
tags: []
