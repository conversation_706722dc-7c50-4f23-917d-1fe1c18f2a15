/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.KegelMeasurementOld', null, global);
goog.exportSymbol('proto.KegelMeasurementOldBuffer', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.KegelMeasurementOld = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.KegelMeasurementOld, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.KegelMeasurementOld.displayName = 'proto.KegelMeasurementOld';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.KegelMeasurementOldBuffer = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.KegelMeasurementOldBuffer.repeatedFields_, null);
};
goog.inherits(proto.KegelMeasurementOldBuffer, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.KegelMeasurementOldBuffer.displayName = 'proto.KegelMeasurementOldBuffer';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.KegelMeasurementOld.prototype.toObject = function(opt_includeInstance) {
  return proto.KegelMeasurementOld.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.KegelMeasurementOld} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.KegelMeasurementOld.toObject = function(includeInstance, msg) {
  var f, obj = {
    resistance: jspb.Message.getFieldWithDefault(msg, 1, 0),
    resistancePositive: jspb.Message.getFieldWithDefault(msg, 2, 0),
    resistanceNegative: jspb.Message.getFieldWithDefault(msg, 3, 0),
    positiveMucus: jspb.Message.getFieldWithDefault(msg, 4, 0),
    positiveReference: jspb.Message.getFieldWithDefault(msg, 5, 0),
    negativeMucus: jspb.Message.getFieldWithDefault(msg, 6, 0),
    negativeReference: jspb.Message.getFieldWithDefault(msg, 7, 0),
    positiveMucusS: jspb.Message.getFieldWithDefault(msg, 8, ""),
    positiveReferenceS: jspb.Message.getFieldWithDefault(msg, 9, ""),
    negativeMucusS: jspb.Message.getFieldWithDefault(msg, 10, ""),
    negativeReferenceS: jspb.Message.getFieldWithDefault(msg, 11, ""),
    bridgeResistorsTemperature: +jspb.Message.getFieldWithDefault(msg, 12, 0.0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.KegelMeasurementOld}
 */
proto.KegelMeasurementOld.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.KegelMeasurementOld;
  return proto.KegelMeasurementOld.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.KegelMeasurementOld} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.KegelMeasurementOld}
 */
proto.KegelMeasurementOld.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setResistance(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setResistancePositive(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setResistanceNegative(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setPositiveMucus(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setPositiveReference(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setNegativeMucus(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setNegativeReference(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setPositiveMucusS(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setPositiveReferenceS(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setNegativeMucusS(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setNegativeReferenceS(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setBridgeResistorsTemperature(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.KegelMeasurementOld.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.KegelMeasurementOld.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.KegelMeasurementOld} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.KegelMeasurementOld.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getResistance();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getResistancePositive();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = message.getResistanceNegative();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getPositiveMucus();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getPositiveReference();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getNegativeMucus();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
  f = message.getNegativeReference();
  if (f !== 0) {
    writer.writeInt32(
      7,
      f
    );
  }
  f = message.getPositiveMucusS();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getPositiveReferenceS();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getNegativeMucusS();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getNegativeReferenceS();
  if (f.length > 0) {
    writer.writeString(
      11,
      f
    );
  }
  f = message.getBridgeResistorsTemperature();
  if (f !== 0.0) {
    writer.writeFloat(
      12,
      f
    );
  }
};


/**
 * optional int32 resistance = 1;
 * @return {number}
 */
proto.KegelMeasurementOld.prototype.getResistance = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.KegelMeasurementOld.prototype.setResistance = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional int32 resistance_positive = 2;
 * @return {number}
 */
proto.KegelMeasurementOld.prototype.getResistancePositive = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.KegelMeasurementOld.prototype.setResistancePositive = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional int32 resistance_negative = 3;
 * @return {number}
 */
proto.KegelMeasurementOld.prototype.getResistanceNegative = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.KegelMeasurementOld.prototype.setResistanceNegative = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int32 positive_mucus = 4;
 * @return {number}
 */
proto.KegelMeasurementOld.prototype.getPositiveMucus = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.KegelMeasurementOld.prototype.setPositiveMucus = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional int32 positive_reference = 5;
 * @return {number}
 */
proto.KegelMeasurementOld.prototype.getPositiveReference = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.KegelMeasurementOld.prototype.setPositiveReference = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional int32 negative_mucus = 6;
 * @return {number}
 */
proto.KegelMeasurementOld.prototype.getNegativeMucus = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.KegelMeasurementOld.prototype.setNegativeMucus = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional int32 negative_reference = 7;
 * @return {number}
 */
proto.KegelMeasurementOld.prototype.getNegativeReference = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.KegelMeasurementOld.prototype.setNegativeReference = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional string positive_mucus_s = 8;
 * @return {string}
 */
proto.KegelMeasurementOld.prototype.getPositiveMucusS = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.KegelMeasurementOld.prototype.setPositiveMucusS = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string positive_reference_s = 9;
 * @return {string}
 */
proto.KegelMeasurementOld.prototype.getPositiveReferenceS = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/** @param {string} value */
proto.KegelMeasurementOld.prototype.setPositiveReferenceS = function(value) {
  jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional string negative_mucus_s = 10;
 * @return {string}
 */
proto.KegelMeasurementOld.prototype.getNegativeMucusS = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/** @param {string} value */
proto.KegelMeasurementOld.prototype.setNegativeMucusS = function(value) {
  jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional string negative_reference_s = 11;
 * @return {string}
 */
proto.KegelMeasurementOld.prototype.getNegativeReferenceS = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/** @param {string} value */
proto.KegelMeasurementOld.prototype.setNegativeReferenceS = function(value) {
  jspb.Message.setProto3StringField(this, 11, value);
};


/**
 * optional float bridge_resistors_temperature = 12;
 * @return {number}
 */
proto.KegelMeasurementOld.prototype.getBridgeResistorsTemperature = function() {
  return /** @type {number} */ (+jspb.Message.getFieldWithDefault(this, 12, 0.0));
};


/** @param {number} value */
proto.KegelMeasurementOld.prototype.setBridgeResistorsTemperature = function(value) {
  jspb.Message.setProto3FloatField(this, 12, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.KegelMeasurementOldBuffer.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.KegelMeasurementOldBuffer.prototype.toObject = function(opt_includeInstance) {
  return proto.KegelMeasurementOldBuffer.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.KegelMeasurementOldBuffer} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.KegelMeasurementOldBuffer.toObject = function(includeInstance, msg) {
  var f, obj = {
    singleMeasurementList: jspb.Message.toObjectList(msg.getSingleMeasurementList(),
    proto.KegelMeasurementOld.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.KegelMeasurementOldBuffer}
 */
proto.KegelMeasurementOldBuffer.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.KegelMeasurementOldBuffer;
  return proto.KegelMeasurementOldBuffer.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.KegelMeasurementOldBuffer} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.KegelMeasurementOldBuffer}
 */
proto.KegelMeasurementOldBuffer.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.KegelMeasurementOld;
      reader.readMessage(value,proto.KegelMeasurementOld.deserializeBinaryFromReader);
      msg.addSingleMeasurement(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.KegelMeasurementOldBuffer.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.KegelMeasurementOldBuffer.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.KegelMeasurementOldBuffer} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.KegelMeasurementOldBuffer.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSingleMeasurementList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.KegelMeasurementOld.serializeBinaryToWriter
    );
  }
};


/**
 * repeated KegelMeasurement single_measurement = 1;
 * @return {!Array<!proto.KegelMeasurementOld>}
 */
proto.KegelMeasurementOldBuffer.prototype.getSingleMeasurementList = function() {
  return /** @type{!Array<!proto.KegelMeasurementOld>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.KegelMeasurementOld, 1));
};


/** @param {!Array<!proto.KegelMeasurementOld>} value */
proto.KegelMeasurementOldBuffer.prototype.setSingleMeasurementList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.KegelMeasurementOld=} opt_value
 * @param {number=} opt_index
 * @return {!proto.KegelMeasurementOld}
 */
proto.KegelMeasurementOldBuffer.prototype.addSingleMeasurement = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.KegelMeasurementOld, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 */
proto.KegelMeasurementOldBuffer.prototype.clearSingleMeasurementList = function() {
  this.setSingleMeasurementList([]);
};


goog.object.extend(exports, proto);
