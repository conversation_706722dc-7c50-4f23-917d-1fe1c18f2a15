/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.KegelCalibration', null, global);
goog.exportSymbol('proto.KegelCalibration.CalibrationVersion', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.KegelCalibration = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.KegelCalibration, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.KegelCalibration.displayName = 'proto.KegelCalibration';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.KegelCalibration.prototype.toObject = function(opt_includeInstance) {
  return proto.KegelCalibration.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.KegelCalibration} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.KegelCalibration.toObject = function(includeInstance, msg) {
  var f, obj = {
    calibrationVersion: jspb.Message.getFieldWithDefault(msg, 1, 0),
    resistivitySlopeMinus1: +jspb.Message.getFieldWithDefault(msg, 2, 0.0),
    resistivityIntercept: +jspb.Message.getFieldWithDefault(msg, 3, 0.0),
    batterySlopeMinus1: +jspb.Message.getFieldWithDefault(msg, 4, 0.0),
    batteryIntercept: +jspb.Message.getFieldWithDefault(msg, 5, 0.0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.KegelCalibration}
 */
proto.KegelCalibration.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.KegelCalibration;
  return proto.KegelCalibration.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.KegelCalibration} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.KegelCalibration}
 */
proto.KegelCalibration.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.KegelCalibration.CalibrationVersion} */ (reader.readEnum());
      msg.setCalibrationVersion(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setResistivitySlopeMinus1(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setResistivityIntercept(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setBatterySlopeMinus1(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setBatteryIntercept(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.KegelCalibration.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.KegelCalibration.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.KegelCalibration} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.KegelCalibration.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getCalibrationVersion();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getResistivitySlopeMinus1();
  if (f !== 0.0) {
    writer.writeFloat(
      2,
      f
    );
  }
  f = message.getResistivityIntercept();
  if (f !== 0.0) {
    writer.writeFloat(
      3,
      f
    );
  }
  f = message.getBatterySlopeMinus1();
  if (f !== 0.0) {
    writer.writeFloat(
      4,
      f
    );
  }
  f = message.getBatteryIntercept();
  if (f !== 0.0) {
    writer.writeFloat(
      5,
      f
    );
  }
};


/**
 * @enum {number}
 */
proto.KegelCalibration.CalibrationVersion = {
  NO_CALIBRATION: 0,
  CALIBRATION_JIGG5_JUNE_2018: 1,
  CALIBRATION_JIGG5_JUNE_2018_BATTERY: 2
};

/**
 * optional CalibrationVersion calibration_version = 1;
 * @return {!proto.KegelCalibration.CalibrationVersion}
 */
proto.KegelCalibration.prototype.getCalibrationVersion = function() {
  return /** @type {!proto.KegelCalibration.CalibrationVersion} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {!proto.KegelCalibration.CalibrationVersion} value */
proto.KegelCalibration.prototype.setCalibrationVersion = function(value) {
  jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional float resistivity_slope_minus_1 = 2;
 * @return {number}
 */
proto.KegelCalibration.prototype.getResistivitySlopeMinus1 = function() {
  return /** @type {number} */ (+jspb.Message.getFieldWithDefault(this, 2, 0.0));
};


/** @param {number} value */
proto.KegelCalibration.prototype.setResistivitySlopeMinus1 = function(value) {
  jspb.Message.setProto3FloatField(this, 2, value);
};


/**
 * optional float resistivity_intercept = 3;
 * @return {number}
 */
proto.KegelCalibration.prototype.getResistivityIntercept = function() {
  return /** @type {number} */ (+jspb.Message.getFieldWithDefault(this, 3, 0.0));
};


/** @param {number} value */
proto.KegelCalibration.prototype.setResistivityIntercept = function(value) {
  jspb.Message.setProto3FloatField(this, 3, value);
};


/**
 * optional float battery_slope_minus_1 = 4;
 * @return {number}
 */
proto.KegelCalibration.prototype.getBatterySlopeMinus1 = function() {
  return /** @type {number} */ (+jspb.Message.getFieldWithDefault(this, 4, 0.0));
};


/** @param {number} value */
proto.KegelCalibration.prototype.setBatterySlopeMinus1 = function(value) {
  jspb.Message.setProto3FloatField(this, 4, value);
};


/**
 * optional float battery_intercept = 5;
 * @return {number}
 */
proto.KegelCalibration.prototype.getBatteryIntercept = function() {
  return /** @type {number} */ (+jspb.Message.getFieldWithDefault(this, 5, 0.0));
};


/** @param {number} value */
proto.KegelCalibration.prototype.setBatteryIntercept = function(value) {
  jspb.Message.setProto3FloatField(this, 5, value);
};


goog.object.extend(exports, proto);
