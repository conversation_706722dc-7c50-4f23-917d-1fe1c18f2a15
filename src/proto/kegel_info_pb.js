/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.BoardRevision', null, global);
goog.exportSymbol('proto.KegelInfo', null, global);
goog.exportSymbol('proto.KegelInfo.ChargerStatus', null, global);
goog.exportSymbol('proto.KegelInfo.DataBufferStatus', null, global);
goog.exportSymbol('proto.KegelInfo.DeviceStatus', null, global);
goog.exportSymbol('proto.KegelInfo.FirmwareVersion', null, global);
goog.exportSymbol('proto.KegelInfo.KegelDiagnostics', null, global);
goog.exportSymbol('proto.KegelInfo.MeasurementStatus', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.KegelInfo = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.KegelInfo, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.KegelInfo.displayName = 'proto.KegelInfo';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.KegelInfo.KegelDiagnostics = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.KegelInfo.KegelDiagnostics, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.KegelInfo.KegelDiagnostics.displayName = 'proto.KegelInfo.KegelDiagnostics';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.KegelInfo.FirmwareVersion = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.KegelInfo.FirmwareVersion, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.KegelInfo.FirmwareVersion.displayName = 'proto.KegelInfo.FirmwareVersion';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.KegelInfo.prototype.toObject = function(opt_includeInstance) {
  return proto.KegelInfo.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.KegelInfo} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.KegelInfo.toObject = function(includeInstance, msg) {
  var f, obj = {
    firmwareVersion: (f = msg.getFirmwareVersion()) && proto.KegelInfo.FirmwareVersion.toObject(includeInstance, f),
    measurementStatus: jspb.Message.getFieldWithDefault(msg, 2, 0),
    deviceStatus: jspb.Message.getFieldWithDefault(msg, 3, 0),
    dataBufferStatus: jspb.Message.getFieldWithDefault(msg, 4, 0),
    dataBufferNofMeasurements: jspb.Message.getFieldWithDefault(msg, 5, 0),
    batteryVoltage: jspb.Message.getFieldWithDefault(msg, 6, 0),
    batteryPercentage: jspb.Message.getFieldWithDefault(msg, 7, 0),
    chargerStatus: jspb.Message.getFieldWithDefault(msg, 8, 0),
    kegelDiagnostics: (f = msg.getKegelDiagnostics()) && proto.KegelInfo.KegelDiagnostics.toObject(includeInstance, f),
    bootloaderVersion: jspb.Message.getFieldWithDefault(msg, 10, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.KegelInfo}
 */
proto.KegelInfo.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.KegelInfo;
  return proto.KegelInfo.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.KegelInfo} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.KegelInfo}
 */
proto.KegelInfo.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.KegelInfo.FirmwareVersion;
      reader.readMessage(value,proto.KegelInfo.FirmwareVersion.deserializeBinaryFromReader);
      msg.setFirmwareVersion(value);
      break;
    case 2:
      var value = /** @type {!proto.KegelInfo.MeasurementStatus} */ (reader.readEnum());
      msg.setMeasurementStatus(value);
      break;
    case 3:
      var value = /** @type {!proto.KegelInfo.DeviceStatus} */ (reader.readEnum());
      msg.setDeviceStatus(value);
      break;
    case 4:
      var value = /** @type {!proto.KegelInfo.DataBufferStatus} */ (reader.readEnum());
      msg.setDataBufferStatus(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDataBufferNofMeasurements(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setBatteryVoltage(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setBatteryPercentage(value);
      break;
    case 8:
      var value = /** @type {!proto.KegelInfo.ChargerStatus} */ (reader.readEnum());
      msg.setChargerStatus(value);
      break;
    case 9:
      var value = new proto.KegelInfo.KegelDiagnostics;
      reader.readMessage(value,proto.KegelInfo.KegelDiagnostics.deserializeBinaryFromReader);
      msg.setKegelDiagnostics(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setBootloaderVersion(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.KegelInfo.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.KegelInfo.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.KegelInfo} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.KegelInfo.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFirmwareVersion();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.KegelInfo.FirmwareVersion.serializeBinaryToWriter
    );
  }
  f = message.getMeasurementStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getDeviceStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = message.getDataBufferStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      4,
      f
    );
  }
  f = message.getDataBufferNofMeasurements();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getBatteryVoltage();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
  f = message.getBatteryPercentage();
  if (f !== 0) {
    writer.writeUint32(
      7,
      f
    );
  }
  f = message.getChargerStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      8,
      f
    );
  }
  f = message.getKegelDiagnostics();
  if (f != null) {
    writer.writeMessage(
      9,
      f,
      proto.KegelInfo.KegelDiagnostics.serializeBinaryToWriter
    );
  }
  f = message.getBootloaderVersion();
  if (f !== 0) {
    writer.writeUint32(
      10,
      f
    );
  }
};


/**
 * @enum {number}
 */
proto.KegelInfo.MeasurementStatus = {
  READY: 0,
  INSERT_TIMER_RUNNING: 1,
  INSERTED: 2,
  WAITING_TO_BE_INSERTED: 3,
  PS_TIMER_RUNNING: 4,
  PROGRAM_SEQUENCE_EXECUTING: 5,
  PROGRAM_SEQUENCE_FINISHED: 6,
  INSERT_TIMED_OUT: 7,
  SENDING_DATA: 8
};

/**
 * @enum {number}
 */
proto.KegelInfo.DataBufferStatus = {
  EMPTY: 0,
  IN_PROGRESS: 1,
  TO_BE_EMPTED: 2,
  FULL: 3
};

/**
 * @enum {number}
 */
proto.KegelInfo.DeviceStatus = {
  ALL_GOOD: 0,
  CHARGING: 1,
  CHARGING_FINISHED: 2,
  BATTERY_LOW: 3,
  OPERATING_TOO_LONG: 4,
  BATTERY_NOT_CHARGING: 5,
  POWER_FAULT: 6,
  NRF_FAULT: 7,
  ELECTRODES_FAULT: 8,
  CHARGER_TIMER_TIMEOUT: 9,
  CHARGER_FAULT: 10,
  CHARGING_TEMPERATURE_HIGH: 11,
  WDT_RESET: 12,
  OTHER_FAULT: 13
};

/**
 * @enum {number}
 */
proto.KegelInfo.ChargerStatus = {
  CHARGER_DISCONNECTED: 0,
  CHARGER_CHARGE_IN_PROGRESS: 1,
  CHARGER_EOC: 2,
  CHARGER_OTHER: 3
};




if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.KegelInfo.KegelDiagnostics.prototype.toObject = function(opt_includeInstance) {
  return proto.KegelInfo.KegelDiagnostics.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.KegelInfo.KegelDiagnostics} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.KegelInfo.KegelDiagnostics.toObject = function(includeInstance, msg) {
  var f, obj = {
    boardRevision: jspb.Message.getFieldWithDefault(msg, 1, 0),
    serialNumber: jspb.Message.getFieldWithDefault(msg, 2, 0),
    wdtApplied: jspb.Message.getFieldWithDefault(msg, 3, 0),
    overtemperatureApplied: jspb.Message.getFieldWithDefault(msg, 4, 0),
    lastChargingStartVoltage: jspb.Message.getFieldWithDefault(msg, 5, 0),
    lastChargingTime: jspb.Message.getFieldWithDefault(msg, 6, 0),
    numberOfChargings: jspb.Message.getFieldWithDefault(msg, 7, 0),
    lastMeasuredVoltage: jspb.Message.getFieldWithDefault(msg, 8, 0),
    lastOperationTemperature: jspb.Message.getFieldWithDefault(msg, 9, 0),
    mtuUsed: jspb.Message.getFieldWithDefault(msg, 10, 0),
    lastChargingTemperature: jspb.Message.getFieldWithDefault(msg, 11, 0),
    lastConnectionIssue: jspb.Message.getFieldWithDefault(msg, 12, 0),
    lastFaultReport: jspb.Message.getFieldWithDefault(msg, 13, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.KegelInfo.KegelDiagnostics}
 */
proto.KegelInfo.KegelDiagnostics.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.KegelInfo.KegelDiagnostics;
  return proto.KegelInfo.KegelDiagnostics.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.KegelInfo.KegelDiagnostics} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.KegelInfo.KegelDiagnostics}
 */
proto.KegelInfo.KegelDiagnostics.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.BoardRevision} */ (reader.readEnum());
      msg.setBoardRevision(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSerialNumber(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setWdtApplied(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setOvertemperatureApplied(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setLastChargingStartVoltage(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setLastChargingTime(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setNumberOfChargings(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setLastMeasuredVoltage(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setLastOperationTemperature(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMtuUsed(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setLastChargingTemperature(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setLastConnectionIssue(value);
      break;
    case 13:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setLastFaultReport(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.KegelInfo.KegelDiagnostics.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.KegelInfo.KegelDiagnostics.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.KegelInfo.KegelDiagnostics} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.KegelInfo.KegelDiagnostics.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getBoardRevision();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getSerialNumber();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getWdtApplied();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getOvertemperatureApplied();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getLastChargingStartVoltage();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
  f = message.getLastChargingTime();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
  f = message.getNumberOfChargings();
  if (f !== 0) {
    writer.writeUint32(
      7,
      f
    );
  }
  f = message.getLastMeasuredVoltage();
  if (f !== 0) {
    writer.writeUint32(
      8,
      f
    );
  }
  f = message.getLastOperationTemperature();
  if (f !== 0) {
    writer.writeUint32(
      9,
      f
    );
  }
  f = message.getMtuUsed();
  if (f !== 0) {
    writer.writeUint32(
      10,
      f
    );
  }
  f = message.getLastChargingTemperature();
  if (f !== 0) {
    writer.writeUint32(
      11,
      f
    );
  }
  f = message.getLastConnectionIssue();
  if (f !== 0) {
    writer.writeUint32(
      12,
      f
    );
  }
  f = message.getLastFaultReport();
  if (f !== 0) {
    writer.writeUint32(
      13,
      f
    );
  }
};


/**
 * optional BoardRevision board_revision = 1;
 * @return {!proto.BoardRevision}
 */
proto.KegelInfo.KegelDiagnostics.prototype.getBoardRevision = function() {
  return /** @type {!proto.BoardRevision} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {!proto.BoardRevision} value */
proto.KegelInfo.KegelDiagnostics.prototype.setBoardRevision = function(value) {
  jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional uint32 serial_number = 2;
 * @return {number}
 */
proto.KegelInfo.KegelDiagnostics.prototype.getSerialNumber = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.KegelInfo.KegelDiagnostics.prototype.setSerialNumber = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 WDT_applied = 3;
 * @return {number}
 */
proto.KegelInfo.KegelDiagnostics.prototype.getWdtApplied = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.KegelInfo.KegelDiagnostics.prototype.setWdtApplied = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 overtemperature_applied = 4;
 * @return {number}
 */
proto.KegelInfo.KegelDiagnostics.prototype.getOvertemperatureApplied = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.KegelInfo.KegelDiagnostics.prototype.setOvertemperatureApplied = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional uint32 last_charging_start_voltage = 5;
 * @return {number}
 */
proto.KegelInfo.KegelDiagnostics.prototype.getLastChargingStartVoltage = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.KegelInfo.KegelDiagnostics.prototype.setLastChargingStartVoltage = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional uint32 last_charging_time = 6;
 * @return {number}
 */
proto.KegelInfo.KegelDiagnostics.prototype.getLastChargingTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.KegelInfo.KegelDiagnostics.prototype.setLastChargingTime = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional uint32 number_of_chargings = 7;
 * @return {number}
 */
proto.KegelInfo.KegelDiagnostics.prototype.getNumberOfChargings = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.KegelInfo.KegelDiagnostics.prototype.setNumberOfChargings = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional uint32 last_measured_voltage = 8;
 * @return {number}
 */
proto.KegelInfo.KegelDiagnostics.prototype.getLastMeasuredVoltage = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/** @param {number} value */
proto.KegelInfo.KegelDiagnostics.prototype.setLastMeasuredVoltage = function(value) {
  jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional uint32 last_operation_temperature = 9;
 * @return {number}
 */
proto.KegelInfo.KegelDiagnostics.prototype.getLastOperationTemperature = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/** @param {number} value */
proto.KegelInfo.KegelDiagnostics.prototype.setLastOperationTemperature = function(value) {
  jspb.Message.setProto3IntField(this, 9, value);
};


/**
 * optional uint32 MTU_used = 10;
 * @return {number}
 */
proto.KegelInfo.KegelDiagnostics.prototype.getMtuUsed = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/** @param {number} value */
proto.KegelInfo.KegelDiagnostics.prototype.setMtuUsed = function(value) {
  jspb.Message.setProto3IntField(this, 10, value);
};


/**
 * optional uint32 last_charging_temperature = 11;
 * @return {number}
 */
proto.KegelInfo.KegelDiagnostics.prototype.getLastChargingTemperature = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/** @param {number} value */
proto.KegelInfo.KegelDiagnostics.prototype.setLastChargingTemperature = function(value) {
  jspb.Message.setProto3IntField(this, 11, value);
};


/**
 * optional uint32 last_connection_issue = 12;
 * @return {number}
 */
proto.KegelInfo.KegelDiagnostics.prototype.getLastConnectionIssue = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 12, 0));
};


/** @param {number} value */
proto.KegelInfo.KegelDiagnostics.prototype.setLastConnectionIssue = function(value) {
  jspb.Message.setProto3IntField(this, 12, value);
};


/**
 * optional uint32 last_fault_report = 13;
 * @return {number}
 */
proto.KegelInfo.KegelDiagnostics.prototype.getLastFaultReport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 13, 0));
};


/** @param {number} value */
proto.KegelInfo.KegelDiagnostics.prototype.setLastFaultReport = function(value) {
  jspb.Message.setProto3IntField(this, 13, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.KegelInfo.FirmwareVersion.prototype.toObject = function(opt_includeInstance) {
  return proto.KegelInfo.FirmwareVersion.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.KegelInfo.FirmwareVersion} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.KegelInfo.FirmwareVersion.toObject = function(includeInstance, msg) {
  var f, obj = {
    major: jspb.Message.getFieldWithDefault(msg, 1, 0),
    minor: jspb.Message.getFieldWithDefault(msg, 2, 0),
    point: jspb.Message.getFieldWithDefault(msg, 3, 0),
    debug: jspb.Message.getFieldWithDefault(msg, 4, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.KegelInfo.FirmwareVersion}
 */
proto.KegelInfo.FirmwareVersion.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.KegelInfo.FirmwareVersion;
  return proto.KegelInfo.FirmwareVersion.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.KegelInfo.FirmwareVersion} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.KegelInfo.FirmwareVersion}
 */
proto.KegelInfo.FirmwareVersion.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMajor(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMinor(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setPoint(value);
      break;
    case 4:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setDebug(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.KegelInfo.FirmwareVersion.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.KegelInfo.FirmwareVersion.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.KegelInfo.FirmwareVersion} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.KegelInfo.FirmwareVersion.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMajor();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getMinor();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getPoint();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getDebug();
  if (f) {
    writer.writeBool(
      4,
      f
    );
  }
};


/**
 * optional uint32 major = 1;
 * @return {number}
 */
proto.KegelInfo.FirmwareVersion.prototype.getMajor = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.KegelInfo.FirmwareVersion.prototype.setMajor = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 minor = 2;
 * @return {number}
 */
proto.KegelInfo.FirmwareVersion.prototype.getMinor = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.KegelInfo.FirmwareVersion.prototype.setMinor = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 point = 3;
 * @return {number}
 */
proto.KegelInfo.FirmwareVersion.prototype.getPoint = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.KegelInfo.FirmwareVersion.prototype.setPoint = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional bool debug = 4;
 * Note that Boolean fields may be set to 0/1 when serialized from a Java server.
 * You should avoid comparisons like {@code val === true/false} in those cases.
 * @return {boolean}
 */
proto.KegelInfo.FirmwareVersion.prototype.getDebug = function() {
  return /** @type {boolean} */ (jspb.Message.getFieldWithDefault(this, 4, false));
};


/** @param {boolean} value */
proto.KegelInfo.FirmwareVersion.prototype.setDebug = function(value) {
  jspb.Message.setProto3BooleanField(this, 4, value);
};


/**
 * optional FirmwareVersion firmware_version = 1;
 * @return {?proto.KegelInfo.FirmwareVersion}
 */
proto.KegelInfo.prototype.getFirmwareVersion = function() {
  return /** @type{?proto.KegelInfo.FirmwareVersion} */ (
    jspb.Message.getWrapperField(this, proto.KegelInfo.FirmwareVersion, 1));
};


/** @param {?proto.KegelInfo.FirmwareVersion|undefined} value */
proto.KegelInfo.prototype.setFirmwareVersion = function(value) {
  jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 */
proto.KegelInfo.prototype.clearFirmwareVersion = function() {
  this.setFirmwareVersion(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.KegelInfo.prototype.hasFirmwareVersion = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional MeasurementStatus measurement_status = 2;
 * @return {!proto.KegelInfo.MeasurementStatus}
 */
proto.KegelInfo.prototype.getMeasurementStatus = function() {
  return /** @type {!proto.KegelInfo.MeasurementStatus} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {!proto.KegelInfo.MeasurementStatus} value */
proto.KegelInfo.prototype.setMeasurementStatus = function(value) {
  jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional DeviceStatus device_status = 3;
 * @return {!proto.KegelInfo.DeviceStatus}
 */
proto.KegelInfo.prototype.getDeviceStatus = function() {
  return /** @type {!proto.KegelInfo.DeviceStatus} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {!proto.KegelInfo.DeviceStatus} value */
proto.KegelInfo.prototype.setDeviceStatus = function(value) {
  jspb.Message.setProto3EnumField(this, 3, value);
};


/**
 * optional DataBufferStatus data_buffer_status = 4;
 * @return {!proto.KegelInfo.DataBufferStatus}
 */
proto.KegelInfo.prototype.getDataBufferStatus = function() {
  return /** @type {!proto.KegelInfo.DataBufferStatus} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {!proto.KegelInfo.DataBufferStatus} value */
proto.KegelInfo.prototype.setDataBufferStatus = function(value) {
  jspb.Message.setProto3EnumField(this, 4, value);
};


/**
 * optional int32 data_buffer_nof_measurements = 5;
 * @return {number}
 */
proto.KegelInfo.prototype.getDataBufferNofMeasurements = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.KegelInfo.prototype.setDataBufferNofMeasurements = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional uint32 battery_voltage = 6;
 * @return {number}
 */
proto.KegelInfo.prototype.getBatteryVoltage = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.KegelInfo.prototype.setBatteryVoltage = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional uint32 battery_percentage = 7;
 * @return {number}
 */
proto.KegelInfo.prototype.getBatteryPercentage = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.KegelInfo.prototype.setBatteryPercentage = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional ChargerStatus charger_status = 8;
 * @return {!proto.KegelInfo.ChargerStatus}
 */
proto.KegelInfo.prototype.getChargerStatus = function() {
  return /** @type {!proto.KegelInfo.ChargerStatus} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/** @param {!proto.KegelInfo.ChargerStatus} value */
proto.KegelInfo.prototype.setChargerStatus = function(value) {
  jspb.Message.setProto3EnumField(this, 8, value);
};


/**
 * optional KegelDiagnostics kegel_diagnostics = 9;
 * @return {?proto.KegelInfo.KegelDiagnostics}
 */
proto.KegelInfo.prototype.getKegelDiagnostics = function() {
  return /** @type{?proto.KegelInfo.KegelDiagnostics} */ (
    jspb.Message.getWrapperField(this, proto.KegelInfo.KegelDiagnostics, 9));
};


/** @param {?proto.KegelInfo.KegelDiagnostics|undefined} value */
proto.KegelInfo.prototype.setKegelDiagnostics = function(value) {
  jspb.Message.setWrapperField(this, 9, value);
};


/**
 * Clears the message field making it undefined.
 */
proto.KegelInfo.prototype.clearKegelDiagnostics = function() {
  this.setKegelDiagnostics(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.KegelInfo.prototype.hasKegelDiagnostics = function() {
  return jspb.Message.getField(this, 9) != null;
};


/**
 * optional uint32 bootloader_version = 10;
 * @return {number}
 */
proto.KegelInfo.prototype.getBootloaderVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/** @param {number} value */
proto.KegelInfo.prototype.setBootloaderVersion = function(value) {
  jspb.Message.setProto3IntField(this, 10, value);
};


/**
 * @enum {number}
 */
proto.BoardRevision = {
  DEVKIT: 0,
  KEGG_REV6: 6,
  KEGG_REV7: 7,
  KEGG_REV8: 8
};

goog.object.extend(exports, proto);
