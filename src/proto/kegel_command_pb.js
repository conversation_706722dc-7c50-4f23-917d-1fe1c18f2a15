/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.Kegel', null, global);
goog.exportSymbol('proto.Kegel.Command', null, global);
goog.exportSymbol('proto.Kegel.Program_Sequence_Params', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Kegel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.Kegel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Kegel.displayName = 'proto.Kegel';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Kegel.Program_Sequence_Params = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.Kegel.Program_Sequence_Params, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Kegel.Program_Sequence_Params.displayName = 'proto.Kegel.Program_Sequence_Params';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Kegel.prototype.toObject = function(opt_includeInstance) {
  return proto.Kegel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Kegel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Kegel.toObject = function(includeInstance, msg) {
  var f, obj = {
    command: jspb.Message.getFieldWithDefault(msg, 1, 0),
    programSequenceData: jspb.Message.getFieldWithDefault(msg, 2, ""),
    programSequenceParams: (f = msg.getProgramSequenceParams()) && proto.Kegel.Program_Sequence_Params.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Kegel}
 */
proto.Kegel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Kegel;
  return proto.Kegel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Kegel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Kegel}
 */
proto.Kegel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.Kegel.Command} */ (reader.readEnum());
      msg.setCommand(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setProgramSequenceData(value);
      break;
    case 3:
      var value = new proto.Kegel.Program_Sequence_Params;
      reader.readMessage(value,proto.Kegel.Program_Sequence_Params.deserializeBinaryFromReader);
      msg.setProgramSequenceParams(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Kegel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Kegel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Kegel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Kegel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getCommand();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getProgramSequenceData();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getProgramSequenceParams();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      proto.Kegel.Program_Sequence_Params.serializeBinaryToWriter
    );
  }
};


/**
 * @enum {number}
 */
proto.Kegel.Command = {
  DO_NOTHING: 0,
  POWER_OFF: 1,
  UPDATE_FIRMWARE: 2,
  UPDATE_KEGEL_INFO: 3,
  INSERT_KEGEL: 4,
  START_PROGRAM_SEQUENCE: 5,
  STOP_PROGRAM_SEQUENCE: 6,
  FACTORY_RESET: 7
};




if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Kegel.Program_Sequence_Params.prototype.toObject = function(opt_includeInstance) {
  return proto.Kegel.Program_Sequence_Params.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Kegel.Program_Sequence_Params} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Kegel.Program_Sequence_Params.toObject = function(includeInstance, msg) {
  var f, obj = {
    measurementInterval: jspb.Message.getFieldWithDefault(msg, 1, 0),
    vibrateInterval: jspb.Message.getFieldWithDefault(msg, 2, 0),
    pauseInterval: jspb.Message.getFieldWithDefault(msg, 3, 0),
    insertionInterval: jspb.Message.getFieldWithDefault(msg, 4, 0),
    psStartInterval: jspb.Message.getFieldWithDefault(msg, 5, 0),
    insertCheck: jspb.Message.getFieldWithDefault(msg, 6, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Kegel.Program_Sequence_Params}
 */
proto.Kegel.Program_Sequence_Params.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Kegel.Program_Sequence_Params;
  return proto.Kegel.Program_Sequence_Params.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Kegel.Program_Sequence_Params} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Kegel.Program_Sequence_Params}
 */
proto.Kegel.Program_Sequence_Params.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setMeasurementInterval(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setVibrateInterval(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setPauseInterval(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setInsertionInterval(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setPsStartInterval(value);
      break;
    case 6:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setInsertCheck(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Kegel.Program_Sequence_Params.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Kegel.Program_Sequence_Params.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Kegel.Program_Sequence_Params} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Kegel.Program_Sequence_Params.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMeasurementInterval();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getVibrateInterval();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = message.getPauseInterval();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getInsertionInterval();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getPsStartInterval();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getInsertCheck();
  if (f) {
    writer.writeBool(
      6,
      f
    );
  }
};


/**
 * optional int32 measurement_interval = 1;
 * @return {number}
 */
proto.Kegel.Program_Sequence_Params.prototype.getMeasurementInterval = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.Kegel.Program_Sequence_Params.prototype.setMeasurementInterval = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional int32 vibrate_interval = 2;
 * @return {number}
 */
proto.Kegel.Program_Sequence_Params.prototype.getVibrateInterval = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.Kegel.Program_Sequence_Params.prototype.setVibrateInterval = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional int32 pause_interval = 3;
 * @return {number}
 */
proto.Kegel.Program_Sequence_Params.prototype.getPauseInterval = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.Kegel.Program_Sequence_Params.prototype.setPauseInterval = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int32 insertion_interval = 4;
 * @return {number}
 */
proto.Kegel.Program_Sequence_Params.prototype.getInsertionInterval = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.Kegel.Program_Sequence_Params.prototype.setInsertionInterval = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional int32 PS_start_interval = 5;
 * @return {number}
 */
proto.Kegel.Program_Sequence_Params.prototype.getPsStartInterval = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.Kegel.Program_Sequence_Params.prototype.setPsStartInterval = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional bool insert_check = 6;
 * Note that Boolean fields may be set to 0/1 when serialized from a Java server.
 * You should avoid comparisons like {@code val === true/false} in those cases.
 * @return {boolean}
 */
proto.Kegel.Program_Sequence_Params.prototype.getInsertCheck = function() {
  return /** @type {boolean} */ (jspb.Message.getFieldWithDefault(this, 6, false));
};


/** @param {boolean} value */
proto.Kegel.Program_Sequence_Params.prototype.setInsertCheck = function(value) {
  jspb.Message.setProto3BooleanField(this, 6, value);
};


/**
 * optional Command command = 1;
 * @return {!proto.Kegel.Command}
 */
proto.Kegel.prototype.getCommand = function() {
  return /** @type {!proto.Kegel.Command} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {!proto.Kegel.Command} value */
proto.Kegel.prototype.setCommand = function(value) {
  jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string Program_Sequence_Data = 2;
 * @return {string}
 */
proto.Kegel.prototype.getProgramSequenceData = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.Kegel.prototype.setProgramSequenceData = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional Program_Sequence_Params program_sequence_params = 3;
 * @return {?proto.Kegel.Program_Sequence_Params}
 */
proto.Kegel.prototype.getProgramSequenceParams = function() {
  return /** @type{?proto.Kegel.Program_Sequence_Params} */ (
    jspb.Message.getWrapperField(this, proto.Kegel.Program_Sequence_Params, 3));
};


/** @param {?proto.Kegel.Program_Sequence_Params|undefined} value */
proto.Kegel.prototype.setProgramSequenceParams = function(value) {
  jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 */
proto.Kegel.prototype.clearProgramSequenceParams = function() {
  this.setProgramSequenceParams(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.Kegel.prototype.hasProgramSequenceParams = function() {
  return jspb.Message.getField(this, 3) != null;
};


goog.object.extend(exports, proto);
