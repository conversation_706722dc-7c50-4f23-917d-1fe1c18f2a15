/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.KegelInfoOld', null, global);
goog.exportSymbol('proto.KegelInfoOld.BoardRevision', null, global);
goog.exportSymbol('proto.KegelInfoOld.ChargerStatus', null, global);
goog.exportSymbol('proto.KegelInfoOld.DeviceStatus', null, global);
goog.exportSymbol('proto.KegelInfoOld.FirmwareVersion', null, global);
goog.exportSymbol('proto.KegelInfoOld.MeasurementStatus', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.KegelInfoOld = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.KegelInfoOld, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.KegelInfoOld.displayName = 'proto.KegelInfoOld';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.KegelInfoOld.FirmwareVersion = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.KegelInfoOld.FirmwareVersion, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.KegelInfoOld.FirmwareVersion.displayName = 'proto.KegelInfoOld.FirmwareVersion';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.KegelInfoOld.prototype.toObject = function(opt_includeInstance) {
  return proto.KegelInfoOld.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.KegelInfoOld} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.KegelInfoOld.toObject = function(includeInstance, msg) {
  var f, obj = {
    boardRevision: jspb.Message.getFieldWithDefault(msg, 1, 0),
    firmwareVersion: (f = msg.getFirmwareVersion()) && proto.KegelInfoOld.FirmwareVersion.toObject(includeInstance, f),
    measurementStatus: jspb.Message.getFieldWithDefault(msg, 3, 0),
    deviceStatus: jspb.Message.getFieldWithDefault(msg, 4, 0),
    dataBufferStatus: jspb.Message.getFieldWithDefault(msg, 5, 0),
    batteryVoltage: jspb.Message.getFieldWithDefault(msg, 6, 0),
    batteryPercentage: jspb.Message.getFieldWithDefault(msg, 7, 0),
    chargerStatus: jspb.Message.getFieldWithDefault(msg, 8, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.KegelInfoOld}
 */
proto.KegelInfoOld.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.KegelInfoOld;
  return proto.KegelInfoOld.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.KegelInfoOld} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.KegelInfoOld}
 */
proto.KegelInfoOld.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.KegelInfoOld.BoardRevision} */ (reader.readEnum());
      msg.setBoardRevision(value);
      break;
    case 2:
      var value = new proto.KegelInfoOld.FirmwareVersion;
      reader.readMessage(value,proto.KegelInfoOld.FirmwareVersion.deserializeBinaryFromReader);
      msg.setFirmwareVersion(value);
      break;
    case 3:
      var value = /** @type {!proto.KegelInfoOld.MeasurementStatus} */ (reader.readEnum());
      msg.setMeasurementStatus(value);
      break;
    case 4:
      var value = /** @type {!proto.KegelInfoOld.DeviceStatus} */ (reader.readEnum());
      msg.setDeviceStatus(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDataBufferStatus(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setBatteryVoltage(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setBatteryPercentage(value);
      break;
    case 8:
      var value = /** @type {!proto.KegelInfoOld.ChargerStatus} */ (reader.readEnum());
      msg.setChargerStatus(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.KegelInfoOld.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.KegelInfoOld.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.KegelInfoOld} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.KegelInfoOld.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getBoardRevision();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getFirmwareVersion();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.KegelInfoOld.FirmwareVersion.serializeBinaryToWriter
    );
  }
  f = message.getMeasurementStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = message.getDeviceStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      4,
      f
    );
  }
  f = message.getDataBufferStatus();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getBatteryVoltage();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
  f = message.getBatteryPercentage();
  if (f !== 0) {
    writer.writeUint32(
      7,
      f
    );
  }
  f = message.getChargerStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      8,
      f
    );
  }
};


/**
 * @enum {number}
 */
proto.KegelInfoOld.BoardRevision = {
  DEVKIT: 0,
  KEGG_REV6: 6,
  KEGG_REV7: 7,
  KEGG_REV8: 8
};

/**
 * @enum {number}
 */
proto.KegelInfoOld.MeasurementStatus = {
  READY: 0,
  INSERT_TIMER_RUNNING: 1,
  INSERTED: 2,
  WAITING_TO_BE_INSERTED: 3,
  PS_TIMER_RUNNING: 4,
  PROGRAM_SEQUENCE_EXECUTING: 5,
  PROGRAM_SEQUENCE_FINISHED: 6,
  INSERT_TIMED_OUT: 7
};

/**
 * @enum {number}
 */
proto.KegelInfoOld.DeviceStatus = {
  ALL_GOOD: 0,
  CHARGING: 1,
  CHARGING_FINISHED: 2,
  BATTERY_LOW: 3,
  OPERATING_TOO_LONG: 4,
  BATTERY_NOT_CHARGING: 5,
  POWER_FAULT: 6,
  NRF_FAULT: 7,
  ELECTRODES_FAULT: 8,
  CHARGER_TIMER_TIMEOUT: 9,
  CHARGER_FAULT: 10,
  OTHER_FAULT: 11
};

/**
 * @enum {number}
 */
proto.KegelInfoOld.ChargerStatus = {
  CHARGER_DISCONNECTED: 0,
  CHARGER_CHARGE_IN_PROGRESS: 1,
  CHARGER_EOC: 2,
  CHARGER_OTHER: 3
};




if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.KegelInfoOld.FirmwareVersion.prototype.toObject = function(opt_includeInstance) {
  return proto.KegelInfoOld.FirmwareVersion.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.KegelInfoOld.FirmwareVersion} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.KegelInfoOld.FirmwareVersion.toObject = function(includeInstance, msg) {
  var f, obj = {
    major: jspb.Message.getFieldWithDefault(msg, 1, 0),
    minor: jspb.Message.getFieldWithDefault(msg, 2, 0),
    point: jspb.Message.getFieldWithDefault(msg, 3, 0),
    debug: jspb.Message.getFieldWithDefault(msg, 4, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.KegelInfoOld.FirmwareVersion}
 */
proto.KegelInfoOld.FirmwareVersion.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.KegelInfoOld.FirmwareVersion;
  return proto.KegelInfoOld.FirmwareVersion.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.KegelInfoOld.FirmwareVersion} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.KegelInfoOld.FirmwareVersion}
 */
proto.KegelInfoOld.FirmwareVersion.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMajor(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMinor(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setPoint(value);
      break;
    case 4:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setDebug(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.KegelInfoOld.FirmwareVersion.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.KegelInfoOld.FirmwareVersion.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.KegelInfoOld.FirmwareVersion} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.KegelInfoOld.FirmwareVersion.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMajor();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getMinor();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getPoint();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getDebug();
  if (f) {
    writer.writeBool(
      4,
      f
    );
  }
};


/**
 * optional uint32 major = 1;
 * @return {number}
 */
proto.KegelInfoOld.FirmwareVersion.prototype.getMajor = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.KegelInfoOld.FirmwareVersion.prototype.setMajor = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 minor = 2;
 * @return {number}
 */
proto.KegelInfoOld.FirmwareVersion.prototype.getMinor = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.KegelInfoOld.FirmwareVersion.prototype.setMinor = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 point = 3;
 * @return {number}
 */
proto.KegelInfoOld.FirmwareVersion.prototype.getPoint = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.KegelInfoOld.FirmwareVersion.prototype.setPoint = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional bool debug = 4;
 * Note that Boolean fields may be set to 0/1 when serialized from a Java server.
 * You should avoid comparisons like {@code val === true/false} in those cases.
 * @return {boolean}
 */
proto.KegelInfoOld.FirmwareVersion.prototype.getDebug = function() {
  return /** @type {boolean} */ (jspb.Message.getFieldWithDefault(this, 4, false));
};


/** @param {boolean} value */
proto.KegelInfoOld.FirmwareVersion.prototype.setDebug = function(value) {
  jspb.Message.setProto3BooleanField(this, 4, value);
};


/**
 * optional BoardRevision board_revision = 1;
 * @return {!proto.KegelInfoOld.BoardRevision}
 */
proto.KegelInfoOld.prototype.getBoardRevision = function() {
  return /** @type {!proto.KegelInfoOld.BoardRevision} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {!proto.KegelInfoOld.BoardRevision} value */
proto.KegelInfoOld.prototype.setBoardRevision = function(value) {
  jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional FirmwareVersion firmware_version = 2;
 * @return {?proto.KegelInfoOld.FirmwareVersion}
 */
proto.KegelInfoOld.prototype.getFirmwareVersion = function() {
  return /** @type{?proto.KegelInfoOld.FirmwareVersion} */ (
    jspb.Message.getWrapperField(this, proto.KegelInfoOld.FirmwareVersion, 2));
};


/** @param {?proto.KegelInfoOld.FirmwareVersion|undefined} value */
proto.KegelInfoOld.prototype.setFirmwareVersion = function(value) {
  jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 */
proto.KegelInfoOld.prototype.clearFirmwareVersion = function() {
  this.setFirmwareVersion(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.KegelInfoOld.prototype.hasFirmwareVersion = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional MeasurementStatus measurement_status = 3;
 * @return {!proto.KegelInfoOld.MeasurementStatus}
 */
proto.KegelInfoOld.prototype.getMeasurementStatus = function() {
  return /** @type {!proto.KegelInfoOld.MeasurementStatus} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {!proto.KegelInfoOld.MeasurementStatus} value */
proto.KegelInfoOld.prototype.setMeasurementStatus = function(value) {
  jspb.Message.setProto3EnumField(this, 3, value);
};


/**
 * optional DeviceStatus device_status = 4;
 * @return {!proto.KegelInfoOld.DeviceStatus}
 */
proto.KegelInfoOld.prototype.getDeviceStatus = function() {
  return /** @type {!proto.KegelInfoOld.DeviceStatus} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {!proto.KegelInfoOld.DeviceStatus} value */
proto.KegelInfoOld.prototype.setDeviceStatus = function(value) {
  jspb.Message.setProto3EnumField(this, 4, value);
};


/**
 * optional int32 data_buffer_status = 5;
 * @return {number}
 */
proto.KegelInfoOld.prototype.getDataBufferStatus = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.KegelInfoOld.prototype.setDataBufferStatus = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional uint32 battery_voltage = 6;
 * @return {number}
 */
proto.KegelInfoOld.prototype.getBatteryVoltage = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.KegelInfoOld.prototype.setBatteryVoltage = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional uint32 battery_percentage = 7;
 * @return {number}
 */
proto.KegelInfoOld.prototype.getBatteryPercentage = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.KegelInfoOld.prototype.setBatteryPercentage = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional ChargerStatus charger_status = 8;
 * @return {!proto.KegelInfoOld.ChargerStatus}
 */
proto.KegelInfoOld.prototype.getChargerStatus = function() {
  return /** @type {!proto.KegelInfoOld.ChargerStatus} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/** @param {!proto.KegelInfoOld.ChargerStatus} value */
proto.KegelInfoOld.prototype.setChargerStatus = function(value) {
  jspb.Message.setProto3EnumField(this, 8, value);
};


goog.object.extend(exports, proto);