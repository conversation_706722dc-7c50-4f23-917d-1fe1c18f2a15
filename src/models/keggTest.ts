import { IKeggTest } from '@/types/keggTest';
import Mongoose from 'mongoose';

// eslint-disable-next-line @typescript-eslint/no-require-imports
const AutoIncrement = require('mongoose-sequence')(Mongoose);

const KeggTestSchema = new Mongoose.Schema<IKeggTest>({
  date: {
    type: Date,
    default: Date.now,
    required: true,
  },
  serial: {
    type: Number,
  },
  success: {
    type: Boolean,
  },
  result: {
    type: Object,
    default: {},
  },
  ip: {
    type: String,
  },
});

KeggTestSchema.plugin(AutoIncrement, { inc_field: 'serial', start_seq: 3500 });
KeggTestSchema.index({ date: 1 });
KeggTestSchema.index({ success: 1 });

const KeggTest = Mongoose.model<IKeggTest>('KeggTest', KeggTestSchema);
export default KeggTest;
