import type { ICalendar } from '@/types/calendar';
import Mongoose from 'mongoose';
const ObjectId = Mongoose.Schema.Types.ObjectId;

const CalendarSchema = new Mongoose.Schema<ICalendar>({
  user: {
    type: ObjectId,
    required: true,
    index: true,
  },
  date: {
    type: Date,
    required: true,
    index: true,
  },
  type: String,
  data: Object,
  values: [Number],
  lastMeasurementTime: String,
  measurements: [{ type: ObjectId, ref: 'Measurement' }],
});

CalendarSchema.index({ user: 1 });
CalendarSchema.index({ date: 1 });
CalendarSchema.index({ user: 1, date: 1 });

const Calendar = Mongoose.model<ICalendar>('Calendar', CalendarSchema);
export default Calendar;
