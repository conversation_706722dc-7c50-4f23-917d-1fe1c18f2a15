import { IMeasurement } from '@/types/measurements';
import Mongoose from 'mongoose';
const ObjectId = Mongoose.Schema.Types.ObjectId;

const MeasurementSchema = new Mongoose.Schema<IMeasurement>({
  user: {
    type: ObjectId,
    required: true,
  },
  date: {
    type: Date,
    default: Date.now,
    required: true,
  },
  serverDate: {
    type: Date,
    default: Date.now,
    required: true,
  },
  keggId: {
    type: String,
  },
  keggInfos: {
    type: [Object],
  },
  sequenceInfo: {
    type: Object,
  },
  resultCode: {
    type: String,
  },
  phoneInfo: {
    platform: {
      type: String,
    },
    os: {
      type: String,
    },
    brand: {
      type: String,
    },
    model: {
      type: String,
    },
  },
  protocolVersion: {
    type: String,
    required: true,
  },
  appVersion: {
    type: String,
  },
  firmwareVersion: {
    type: String,
  },
  temperature: {
    type: Number,
  },
  data: {
    type: [Object],
  },
  rawData: {
    type: [Object],
  },
  resistanceAvg: {
    type: Number,
  },
  measurementValue: {
    type: Number,
  },
  computedValues: Object,
  session: {
    type: [Object],
  },
  lastMeasurement: {
    type: Boolean,
    default: true,
  },
});

MeasurementSchema.index({ user: 1 });
MeasurementSchema.index({ date: 1 });
MeasurementSchema.index({ appVersion: 1 });
MeasurementSchema.index({ firmwareVersion: 1 });
MeasurementSchema.index({ user: 1, date: 1 });
MeasurementSchema.index({ user: 1, appVersion: 1 });
MeasurementSchema.index({ user: 1, firmwareVersion: 1 });
MeasurementSchema.index({ user: 1, lastMeasurement: 1 });

const Measurement = Mongoose.model<IMeasurement>('Measurement', MeasurementSchema);
export default Measurement;
