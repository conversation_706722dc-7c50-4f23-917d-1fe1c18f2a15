import bcrypt from 'bcrypt';
import fs from 'fs';
import jwt from 'jsonwebtoken';
import Mongoose, { Model, Schema } from 'mongoose';

import { Consts } from '@/consts';
import Calendar from '@/models/calendar';
import Measurement from '@/models/measurement';
import { IUser } from '@/types/user';

const ObjectId = Schema.Types.ObjectId;

const tokenDirectory = 'config/token';

interface IUserModel extends Model<IUser> {
  authenticate(email: string, password: string): Promise<IUser>;
  makeToken(userId: string): Promise<string>;
  getUserIdFromToken(token: string): Promise<string>;
  getUserFromToken(token: string): Promise<IUser>;
}

const UserSchema = new Schema<IUser, IUserModel>({
  email: {
    type: String,
    unique: true,
    required: true,
    trim: true,
  },
  name: {
    type: String,
    trim: true,
  },
  password: {
    type: String,
  },
  created: {
    type: Date,
    default: () => new Date(),
    required: true,
  },
  lastActivity: {
    type: Date,
    default: () => new Date(),
    required: true,
  },
  enabled: {
    type: Boolean,
    default: true,
    required: true,
  },
  emailVerified: {
    type: Boolean,
  },
  test: {
    type: Boolean,
    default: false,
    required: true,
  },
  role: {
    type: String,
    required: true,
    default: 'user',
  },
  phone: {
    type: String,
  },
  group: {
    type: String,
  },
  img: { data: Buffer, contentType: String },
  info: {
    type: Object,
  },
  token: {
    type: String,
    default: null,
  },
  linkedAccount: {
    type: {
      type: String,
    },
    id: {
      type: String,
    },
    token: {
      type: String,
    },
  },
  tempPasswordToken: {
    type: String,
  },
  tempPasswordTokenValidity: {
    type: Date,
  },
  measurements: [{ type: ObjectId, ref: 'Measurement' }],
  calendar: [{ type: ObjectId, ref: 'Calendar' }],
  cycleCount: {
    type: Number,
    default: 0,
  },
});

UserSchema.index({ email: 1 });
UserSchema.index({ created: 1 });
UserSchema.index({ test: 1 });
UserSchema.index({ 'info.appVersion': 1 });
UserSchema.index({ 'info.lastKeggFirmwareVersion': 1 });
UserSchema.index({ 'info.profile.firstName': 1 });
UserSchema.index({ 'info.profile.lastName': 1 });
UserSchema.index({ 'info.keggIdentifier': 1 });

UserSchema.statics.authenticate = async function (email, password) {
  const escapedEmail = email.replace('+', '\\+');
  const user = await User.findOne({ email: { $regex: new RegExp(escapedEmail, 'i') } });

  if (user == null) {
    const error = new Error('User not found');
    throw error;
  }

  if (!user.password) {
    throw new Error('Password is not set');
  }

  const result = await bcrypt.compare(password, user.password);

  if (result === true) {
    return user;
  } else {
    console.log('Incorrect password: ' + user._id);
    throw Error('Incorrect password');
  }
};
type KeyType = string | Buffer;

UserSchema.statics.makeToken = async function (userId) {
  let key: KeyType | undefined = process.env['JWT_PRIVATE_KEY'];
  if (key == null) {
    if (fs.existsSync(tokenDirectory + '/private.key')) {
      key = fs.readFileSync(tokenDirectory + '/private.key');
    } else {
      key = fs.readFileSync(tokenDirectory + '/dev-private.key');
    }
  }

  return jwt.sign({ id: userId, r: Math.random() }, key, { algorithm: 'RS256' });
};

UserSchema.statics.getUserIdFromToken = async function (token) {
  let key: KeyType | undefined = process.env['JWT_PUBLIC_KEY'];
  if (key == null) {
    if (fs.existsSync(tokenDirectory + '/public.key')) {
      key = fs.readFileSync(tokenDirectory + '/public.key');
    } else {
      key = fs.readFileSync(tokenDirectory + '/dev-public.key');
    }
  }

  const decoded = jwt.verify(token, key) as jwt.JwtPayload;

  if (decoded == null || decoded.id == null) {
    console.log('Invalid token');
    throw Error('Invalid token');
  }

  return decoded.id;
};

UserSchema.statics.getUserFromToken = async function (token) {
  const userId = await User.getUserIdFromToken(token);
  const user = await User.findById(userId);

  if (user == null) {
    console.log('User not found');
    throw Error('User not found');
  }

  return user;
};

UserSchema.pre('findOneAndDelete', async function (next) {
  const user = this.getFilter();

  await Calendar.deleteMany({ user: user._id });
  await Measurement.deleteMany({ user: user._id });
  next();
});

const User = Mongoose.model<IUser, IUserModel>('User', UserSchema);
export default User;
