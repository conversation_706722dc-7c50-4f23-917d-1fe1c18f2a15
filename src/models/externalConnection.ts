import { IExternalConnection } from '@/types/externalConnection';
import Mongoose from 'mongoose';
const ObjectId = Mongoose.Schema.Types.ObjectId;

const ExternalConnectionSchema = new Mongoose.Schema<IExternalConnection>({
  user: {
    type: ObjectId,
    required: true,
    index: true,
  },
  created: {
    type: Date,
    required: true,
    index: true,
  },
  lastSynchronization: {
    type: Date,
    required: false,
    index: true,
  },
  type: String,
  data: Object,
});

ExternalConnectionSchema.index({ user: 1 });

const ExternalConnection = Mongoose.model<IExternalConnection>('ExternalConnection', ExternalConnectionSchema);
export default ExternalConnection;
