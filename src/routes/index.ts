import { Consts } from '@/consts';
import express from 'express';

const router = express.Router();

/**
 * @swagger
 * /:
 *   get:
 *     summary: Get basic application info
 *     description: Returns app name, API version, environment, and deployment timestamp.
 *     tags:
 *       - Index
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     responses:
 *       200:
 *         description: Basic application info.
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: "AppName v1.0.0 production 2024-11-11T12:00:00Z"
 */
router.get('/', function (req, res) {
  res.send(Consts.appName + ' ' + Consts.apiVersion + ' ' + Consts.environment + ' ' + Consts.deployTimestamp);
});

/**
 * @swagger
 * /appVersionRequirement:
 *   get:
 *     summary: Get app version requirements
 *     description: Returns the minimum and recommended app versions.
 *     tags:
 *       - Index
 *     responses:
 *       200:
 *         description: App version requirements.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 minimumAppVersion:
 *                   type: string
 *                   example: '1.0'
 *                 recommendedVersion:
 *                   type: string
 *                   example: '1.0'
 */
router.get('/appVersionRequirement', function (req, res) {
  res.json({ minimumAppVersion: '1.0', recommendedVersion: '1.0' });
});

export default router;
