import express, { Request, Response } from 'express';

import { Consts } from '@/consts';
import User from '@/models/user';
import calendarService, {
  AddOrUpdateCalendarProps,
  GetCalendarFeedParams,
  RemoveCalendarProps,
  UpdateCalendarProps,
} from '@/services/calendar_service';
import Mixpanel from 'mixpanel';
import { toEditorSettings } from 'typescript';

const router = express.Router();

let mixpanelApi: Mixpanel.Mixpanel | null = null;
if (Consts.environment == 'live') {
  mixpanelApi = Mixpanel.init(Consts.mixPanelToken);
}

const remove = async function (req: Request, res: Response) {
  try {
    const token = req.headers['x-access-token'] as string | undefined;
    if (!token) {
      res.json({ success: false, message: 'Token is missing' });
      return;
    }
    const user = await User.getUserFromToken(token);

    if (user.role != 'admin') {
      res.json({ success: false, message: 'Unauthorized' });
      return;
    }

    const props: RemoveCalendarProps = {
      userId: req.body.user,
      date: req.body.date,
    };

    res.json(await calendarService.removeCalendar(props));
    return;
  } catch (error) {
    console.log('Calendar remove error: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const updatePeriods = async (req: Request, res: Response) => {
  try {
  const token = req.headers['x-access-token'] as string | undefined;
  if (!token) {
    res.json({ success: false, message: 'Token is missing' });
    return;
  }
  
    const user = await User.getUserFromToken(token);
    res.json(await calendarService.updatePeriods({ user, periods: req.body.periods }));
    return;
  } catch (error) {
    console.log('Calendar update periods error: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const update = async (req: Request, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string | undefined;
    if (!token) {
      res.json({ success: false, message: 'Token is missing' });
      return;
    }
    const user = await User.getUserFromToken(token);

    if (user.role != 'admin' && user.role != 'support') {
      res.json({ success: false, message: 'Unauthorized' });
      return;
    }

    const props: UpdateCalendarProps = {
      userId: req.body.user,
      date: req.body.date,
      data: req.body.data,
    };

    res.json(await calendarService.updateCalendar(props));
    return;
  } catch (error) {
    console.log('Calendar update error: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const addOrUpdateCalendar = async (req: Request, res: Response) => {
  try {
    if (req.body.date == null) {
      res.json({ success: false, message: 'Missing data' });
      return;
    }

    const token = req.headers['x-access-token'] as string | undefined;
    if (!token) {
      res.json({ success: false, message: 'Token is missing' });
      return;
    }

    let user;
    if (req.body.user != null) {
      user = await User.findOne({ _id: req.body.user });
    } else {
      user = await User.getUserFromToken(token);
    }

    if (user == null) {
      res.json({ success: false, message: 'User not found' });
      return;
    }

    const props: AddOrUpdateCalendarProps = {
      user: user,
      date: req.body.date,
      type: req.body.type,
      data: req.body.data,
      signupData: req.body.signupData,
      mixpanelApi: mixpanelApi,
    };

    res.json(await calendarService.addOrUpdateCalendar(props));
    return;
  } catch (error) {
    console.log('Calendar update calendar error: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const list = async (req: Request, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string | undefined;
    if (!token) {
      res.json({ success: false, message: 'Token is missing' });
      return;
    }
    const user = await User.getUserFromToken(token);

    res.json(
      await calendarService.getCalendarData({ userId: user._id.toString(), userRole: user.role, body: req.body }),
    );
    return;
  } catch (error) {
    console.log('Calendar list error: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const feed = async (req: Request, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string | undefined;
    if (!token) {
      res.json({ success: false, message: 'Token is missing' });
      return;
    }
    const user = await User.getUserFromToken(token);

    const query: GetCalendarFeedParams = {};
    if (user.role == 'admin' || user.role == 'support' || user.role == 'readonly') {
      if (req.body.id != null) {
        query._id = req.body.id;
      } else {
        if (req.body.user != null) {
          query.user = req.body.user;
        }
      }
    } else {
      query.user = user._id.toString();
    }

    res.json(await calendarService.getCalendarFeed(query));
    return;
  } catch (error) {
    console.log('Calendar feed error: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const feedV2 = async (req: Request, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string | undefined;
    if (!token) {
      res.json({ success: false, message: 'Token is missing' });
      return;
    }
    const user = await User.getUserFromToken(token);

    const query: GetCalendarFeedParams = {};
    if (user.role == 'admin' || user.role == 'support' || user.role == 'readonly') {
      if (req.body.id != null) {
        query._id = req.body.id;
      } else {
        if (req.body.user != null) {
          query.user = req.body.user;
        }
      }
    } else {
      query.user = user._id.toString();
    }

    const testing = req.body.testing ?? false;

    res.json(await calendarService.getCalendarFeedV2(query, testing));
    return;
  } catch (error) {
    console.log('Calendar feed error: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const predictionsV2 = async (req: Request, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string | undefined;
    if (!token) {
      res.json({ success: false, message: 'Token is missing' });
      return;
    }

    let userId: string = "";
    const user = await User.getUserFromToken(token);

    if (user.role == 'admin' || user.role == 'support' || user.role == 'readonly') {
      if (req.body.userId != null) {
        userId = req.body.userId;
      } else {
        userId = user._id.toString();
      }
    } else {
      userId = user._id.toString();
    } 

    const cycles = Number.isInteger(Number(req.body.cycles)) ? Number(req.body.cycles) : 4;
    const testing = req.body.testing ?? false;
    res.json(await calendarService.getCyclePredictionsAndromeda(userId, cycles, testing));
    return;

  } catch (error) {
    console.log('Calendar predictions Andromeda error: ' + error);
    res.json({ success: false, message: error
    });
    return;
  }
};

const predictionsV2Input = async (req: Request, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string | undefined;
    if (!token) {
      res.json({ success: false, message: 'Token is missing' });
      return;
    }

    let userId: string = "";
    const user = await User.getUserFromToken(token);

    if (user.role == 'admin' || user.role == 'support' || user.role == 'readonly') {
      if (req.body.userId != null) {
        userId = req.body.userId;
      } else {
        userId = user._id.toString();
      }
    } else {
      userId = user._id.toString();
    } 

    const cycles = Number.isInteger(Number(req.body.cycles)) ? Number(req.body.cycles) : 4;
    res.json(await calendarService.prepareCycleRequest(userId, cycles));
    return;

  } catch (error) {
    console.log('Calendar predictions Andromeda error: ' + error);
    res.json({ success: false, message: error
    });
    return;
  }
};



/**
 * @swagger
 * /calendar:
 *   post:
 *     summary: List calendar items
 *     description: Retrieve a list of calendar items based on the user's role, user ID, date range, pagination, and sorting options.
 *     tags:
 *       - Calendar
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: Calendar item ID to filter by, if applicable.
 *               user:
 *                 type: string
 *                 description: User ID to filter calendar items by user.
 *               startDate:
 *                 type: string
 *                 format: date
 *                 description: Start date for filtering calendar items (YYYY-MM-DD).
 *               endDate:
 *                 type: string
 *                 format: date
 *                 description: End date for filtering calendar items (YYYY-MM-DD).
 *               pagesize:
 *                 type: integer
 *                 description: Number of items per page.
 *               page:
 *                 type: integer
 *                 description: Page number for pagination.
 *               sort:
 *                 type: integer
 *                 description: Sort order for date (-1 for descending, 1 for ascending).
 *     responses:
 *       200:
 *         description: Success - List of calendar items.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       date:
 *                         type: string
 *                         example: "2024-11-11"
 *                       data:
 *                         type: object
 *                         description: Calendar data.
 *                       values:
 *                         type: array
 *                         description: List of values.
 *                         items:
 *                           type: string
 *                       lastMeasurementTime:
 *                         type: string
 *                         format: date-time
 *                         description: Last measurement timestamp.
 *       400:
 *         description: Error - Bad Request.
 *       500:
 *         description: Error - Server Error.
 */
router.post('/', list);
/**
 * @swagger
 * /calendar/feed:
 *   post:
 *     summary: Retrieve calendar data with prediction and ovulation data
 *     description: This endpoint retrieves calendar data based on user role, token, and optional filtering by user ID or date range. The response includes both calendar items and prediction data for fertility and ovulation.
 *     tags:
 *       - Calendar
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: Calendar item ID to filter by, if applicable.
 *               user:
 *                 type: string
 *                 description: User ID to filter calendar items by user.
 *     responses:
 *       200:
 *         description: Success - Calendar data with predictions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   additionalProperties:
 *                     type: object
 *                     properties:
 *                       date:
 *                         type: string
 *                         example: "2024-11-11"
 *                       prediction:
 *                         type: object
 *                         properties:
 *                           fertile:
 *                             type: boolean
 *                             example: true
 *                           ovulation:
 *                             type: boolean
 *                             example: true
 *                           period:
 *                             type: boolean
 *                             example: true
 *                       past:
 *                         type: object
 *                         properties:
 *                           ovulation:
 *                             type: boolean
 *                             example: true
 *                           beforeOvulation:
 *                             type: boolean
 *                             example: true
 *                           afterOvulation:
 *                             type: boolean
 *                             example: false
 *       400:
 *         description: Error - Token missing or invalid request data
 *       500:
 *         description: Error - Server Error
 */
router.post('/feed', feed);
/**
 * @swagger
 * /calendar/set:
 *   post:
 *     summary: Add or update a calendar entry
 *     description: This endpoint adds or updates a user's calendar entry based on the provided date. It can also create multiple days of data for a new user during onboarding.
 *     tags:
 *       - Calendar
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               date:
 *                 type: string
 *                 description: Date of the calendar entry in ISO format.
 *               type:
 *                 type: string
 *                 description: Type of calendar data.
 *               data:
 *                 type: object
 *                 description: Data object for the calendar entry.
 *               signupData:
 *                 type: object
 *                 description: Signup data for onboarding, if applicable.
 *                 properties:
 *                   keggIdentifier:
 *                     type: string
 *                     description: Unique identifier for the user.
 *               user:
 *                 type: string
 *                 description: (Optional) User ID if specifying a different user.
 *     responses:
 *       200:
 *         description: Success - Calendar item added or updated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Ok
 *                 item:
 *                   type: object
 *                   description: Calendar item details (returned only if updated).
 *                   properties:
 *                     date:
 *                       type: string
 *                       example: "2024-11-11"
 *                     data:
 *                       type: object
 *                       description: Data related to the calendar entry.
 *                     values:
 *                       type: array
 *                       items:
 *                         type: number
 *                       example: [37.5, 37.6, 37.4]
 *       400:
 *         description: Error - Missing or invalid request data
 *       500:
 *         description: Error - Server Error
 */
router.post('/set', addOrUpdateCalendar);
/**
 * @swagger
 * /calendar/setPeriods:
 *   post:
 *     summary: Update period information for a user's calendar
 *     description: This endpoint updates the period information for multiple dates in the user's calendar.
 *     tags:
 *       - Calendar
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               periods:
 *                 type: object
 *                 description: A map of dates to period indicators (1 for period, 0 for no period).
 *                 example:
 *                   "2024-11-11": 1
 *                   "2024-11-12": 0
 *                 additionalProperties:
 *                   type: integer
 *                   enum: [0, 1]
 *                   description: 1 indicates a period day, 0 indicates no period.
 *     responses:
 *       200:
 *         description: Success - Period data updated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *       400:
 *         description: Error - Missing or invalid request data
 *       500:
 *         description: Error - Server Error
 */
router.post('/setPeriods', updatePeriods);
/**
 * @swagger
 * /calendar/update:
 *   post:
 *     summary: Update calendar data for a specific date
 *     description: Updates the calendar data for a specific user and date. Only accessible by admin and support roles.
 *     tags:
 *       - Calendar
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user:
 *                 type: string
 *                 description: The ID of the user whose calendar data is being updated.
 *                 example: "user12345"
 *               date:
 *                 type: string
 *                 format: date
 *                 description: The date of the calendar entry to update (YYYY-MM-DD).
 *                 example: "2024-11-11"
 *               data:
 *                 type: object
 *                 description: The updated calendar data for the specified date.
 *                 example:
 *                   period: true
 *                   valueAtLogin: true
 *     responses:
 *       200:
 *         description: Success - Calendar data updated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ok"
 *       400:
 *         description: Error - Missing or invalid request data
 *       401:
 *         description: Error - Unauthorized access
 *       404:
 *         description: Error - Calendar entry not found
 *       500:
 *         description: Error - Server Error
 */
router.post('/update', update);
/**
 * @swagger
 * /calendar/remove:
 *   delete:
 *     summary: Remove a calendar entry for a specific date
 *     description: Deletes a specified calendar entry for a user by date. Only accessible by admin role.
 *     tags:
 *       - Calendar
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user:
 *                 type: string
 *                 description: The ID of the user whose calendar entry is being deleted.
 *                 example: "user12345"
 *               date:
 *                 type: string
 *                 format: date
 *                 description: The date of the calendar entry to remove (YYYY-MM-DD).
 *                 example: "2024-11-11"
 *     responses:
 *       200:
 *         description: Success - Calendar entry removed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ok"
 *       400:
 *         description: Error - Missing or invalid request data
 *       401:
 *         description: Error - Unauthorized access
 *       404:
 *         description: Error - Calendar entry not found
 *       500:
 *         description: Error - Server error
 */
router.post('/remove', remove);

/**
 * @swagger
 * /calendar/andromeda:
 *   post:
 *     summary: Retrieve cycle predictions using the Andromeda algorithm
 *     description: >
 *       This endpoint returns cycle predictions based on the authenticated user's token and role.
 *       For users with the roles `admin`, `support`, or `readonly`, an alternative user can be specified
 *       via the request body parameters `id` and `userId`. The number of cycles to predict can also be customized;
 *       if not provided or invalid, it defaults to 4.
 *     tags:
 *       - Calendar
 *     parameters:
 *       - in: header
 *         name: x-access-token
 *         description: Access token for authentication.
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *                 description: >
 *                   The user ID for which predictions are required.
 *                   This is used in conjunction with the `id` parameter for privileged users.
 *               cycles:
 *                 type: integer
 *                 description: Number of cycles for which predictions should be computed.
 *                 default: 4
 *               testing:
 *                 type: boolean
 *                 description: Flag to indicate if this is a test request.
 *                 default: false
 *             example:
 *               id: "optional-id"
 *               userId: "user12345"
 *               cycles: 10
 *               testing: false
 *     responses:
 *       200:
 *         description: Successfully retrieved cycle predictions.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   description: Object containing the predicted cycle data.
 *       400:
 *         description: Bad Request – Missing token or invalid input parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Token is missing"
 *       500:
 *         description: Internal Server Error – An error occurred while processing the predictions.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */
router.post('/andromeda', predictionsV2);

/**
 * @swagger
 * /calendar/feedV2:
 *   post:
 *     summary: Retrieve calendar data with updated fertility predictions
 *     description: >
 *       This endpoint retrieves calendar data using the new prediction engine.
 *       In addition to the standard calendar items (including past period data),
 *       extra days with non-zero fertility probabilities are added. For each day,
 *       the response includes the fertility probability and a flag indicating if
 *       the day is within the fertile window. (Note: period predictions from the new
 *       engine are not yet integrated; they are derived solely from past calendar data.)
 *     tags:
 *       - Calendar
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: Calendar item ID to filter by, if applicable.
 *               user:
 *                 type: string
 *                 description: User ID to filter calendar items by user.
 *               testing:
 *                 type: boolean
 *                 description: Flag to indicate if this is a test request.
 *                 default: false
 *     responses:
 *       200:
 *         description: Success - Calendar data with new predictions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 lastPeriodStartDay:
 *                   type: string
 *                   example: "2025-01-06"
 *                 nextPeriodStartDay:
 *                   type: string
 *                   nullable: true
 *                   example: null
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       date:
 *                         type: string
 *                         example: "2025-01-06"
 *                       lastMeasurementTime:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-01-06T08:20:00Z"
 *                       data:
 *                         type: object
 *                         description: Calendar data such as period information.
 *                         example: { "period": true, "notes": "User recorded period" }
 *                       values:
 *                         type: array
 *                         items:
 *                           type: number
 *                         example: [36.6]
 *                       prediction:
 *                         type: object
 *                         properties:
 *                           fertilityProbability:
 *                             type: number
 *                             example: 0.28
 *                           ovulationProbability:
 *                             type: number
 *                             example: 0.12
 *                           fertile:
 *                             type: boolean
 *                             example: true
 *                           fertileWindow:
 *                             type: boolean
 *                             example: true
 *                           period:
 *                             type: boolean
 *                             example: true
 *                           ovulation:
 *                             type: boolean
 *                             example: false
 *       400:
 *         description: Error - Token missing or invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Token is missing"
 *       500:
 *         description: Error - Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */
router.post('/feedV2', feedV2);

//only for testing
router.post('/andromedaInput', predictionsV2Input);

export default router;
