import User from '@/models/user';
import externalConnectionService from '@/services/external_connection_service';
import express, { Request, Response } from 'express';

const router = express.Router();

const list = async (req: Request, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string;

    const result = await externalConnectionService.getExternalConnections(token);

    if (result.success) {
      res.json({ success: true, message: 'Ok', connections: result.connections });
    } else {
      res.status(400).json({ success: false, message: result.message });
    }
  } catch (error) {
    console.error('Error in list external connections controller:', error);
    res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

const create = async (req: Request, res: Response) => {
  if (!req.body.type || !req.body.token) {
    res.json({ success: false, message: 'Missing fields' });
    return;
  }

  try {
    const token = req.headers['x-access-token'] as string;
    const { type, token: code } = req.body;

    const result = await externalConnectionService.createExternalConnection(token, type, code);

    if (result.success) {
      res.json({ success: true, message: 'Ok', connection: result.connection });
    } else {
      res.json({ success: false, message: result.message });
    }
  } catch (error) {
    console.error('Error in create external connection controller:', error);
    res.json({ success: false, message: 'Internal server error' });
  }
};

const synchronize = async (req: Request, res: Response) => {
  if (req.body.id == null) {
    res.json({ success: false, message: 'Missing fields' });
    return;
  }
  const token = req.headers['x-access-token'] as string;
  const user = await User.getUserFromToken(token);

  try {
    const items = await externalConnectionService.synchronizeExternalConnection(
      user._id.toString(),
      req.body.id,
      req.body.unit,
    );
    res.json({ success: true, message: 'Ok', items });
  } catch (error) {
    console.log('Synchronization error:', error);
    res.json({ success: false, message: (error as Error).message });
  }
};

const remove = async (req: Request, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string;
    const connectionId = req.body.id;

    const result = await externalConnectionService.removeExternalConnection(token, connectionId);

    res.json(result);
  } catch (error) {
    console.error('Error in remove controller:', error);
    res.json({ success: false, message: (error as Error).message || 'An error occurred' });
  }
};

/**
 * @swagger
 * /externalConnection:
 *   post:
 *     summary: Retrieve external connections for the authenticated user
 *     description: Retrieves a list of external connections associated with the authenticated user.
 *     tags:
 *       - ExternalConnection
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     responses:
 *       200:
 *         description: Success - External connections retrieved
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ok"
 *                 connections:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         description: Connection ID
 *                         example: "connection12345"
 *                       user:
 *                         type: string
 *                         description: ID of the associated user
 *                         example: "user12345"
 *                       provider:
 *                         type: string
 *                         description: The external provider name
 *                         example: "provider_name"
 *                       token:
 *                         type: string
 *                         description: Access token for the external provider (sensitive data)
 *                         example: "some_access_token"
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                         description: Date when the connection was created
 *                         example: "2024-01-01T12:00:00Z"
 *       401:
 *         description: Error - Unauthorized access
 *       500:
 *         description: Error - Server error
 */
router.post('/', list);
/**
 * @swagger
 * /externalConnection/create:
 *   post:
 *     summary: Create a new external connection
 *     description: Creates a new external connection for the authenticated user and removes any existing connections of the same type.
 *     tags:
 *       - ExternalConnection
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 description: Type of the external connection (e.g., "tempdrop").
 *                 example: "tempdrop"
 *               token:
 *                 type: string
 *                 description: Authorization token received from the external provider.
 *                 example: "authorization_code_123"
 *             required:
 *               - type
 *               - token
 *     responses:
 *       200:
 *         description: Success - External connection created
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ok"
 *                 connection:
 *                   type: object
 *                   properties:
 *                     _id:
 *                       type: string
 *                       description: Connection ID
 *                       example: "connection12345"
 *                     user:
 *                       type: string
 *                       description: User ID
 *                       example: "user12345"
 *                     type:
 *                       type: string
 *                       description: Type of the connection
 *                       example: "tempdrop"
 *                     created:
 *                       type: string
 *                       format: date-time
 *                       description: Date when the connection was created
 *                       example: "2024-01-01T12:00:00Z"
 *                     lastSynchronization:
 *                       type: string
 *                       format: date-time
 *                       nullable: true
 *                       description: Last synchronization date (null if not synchronized yet)
 *                       example: null
 *       400:
 *         description: Error - Missing fields in the request body
 *       401:
 *         description: Error - Unauthorized access
 *       500:
 *         description: Error - Server error
 */
router.post('/create', create);
/**
 * @swagger
 * /externalConnection/synchronize:
 *   post:
 *     summary: Synchronize external connection data
 *     description: Fetches and synchronizes data from an external source for a specified connection.
 *     tags:
 *       - ExternalConnection


 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
*     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: The ID of the external connection to synchronize.
 *                 example: "connection12345"
 *               unit:
 *                 type: string
 *                 description: Unit of temperature, either 'C' for Celsius or 'F' for Fahrenheit.
 *                 example: "C"
 *             required:
 *               - id
 *     responses:
 *       200:
 *         description: Synchronization successful - returns synchronized items.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ok"
 *                 items:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       date:
 *                         type: string
 *                         description: The date of the measurement in YYYY-MM-DD format.
 *                         example: "2024-11-10"
 *                       data:
 *                         type: object
 *                         description: Data associated with the measurement, including temperature.
 *                         properties:
 *                           temperature:
 *                             type: object
 *                             properties:
 *                               value:
 *                                 type: number
 *                                 example: 36.7
 *                               source:
 *                                 type: string
 *                                 example: "tempdrop"
 *                       values:
 *                         type: array
 *                         items:
 *                           type: number
 *                         description: Historical values for the date.
 *                         example: [36.5, 36.6]
 *                       lastMeasurementTime:
 *                         type: string
 *                         format: date-time
 *                         description: Timestamp of the last measurement.
 *                         example: "2024-11-10T08:30:00Z"
 *       400:
 *         description: Error - Missing required fields.
 *       401:
 *         description: Error - Unauthorized access.
 *       500:
 *         description: Error - Server error during synchronization.
 */
router.post('/synchronize', synchronize);
/**
 * @swagger
 * /externalConnection/synchornize:
 *   post:
 *     summary: Synchronize external connection data
 *     description: Fetches and synchronizes data from an external source for a specified connection.
 *     tags:
 *       - ExternalConnection


 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
*     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: The ID of the external connection to synchronize.
 *                 example: "connection12345"
 *               unit:
 *                 type: string
 *                 description: Unit of temperature, either 'C' for Celsius or 'F' for Fahrenheit.
 *                 example: "C"
 *             required:
 *               - id
 *     responses:
 *       200:
 *         description: Synchronization successful - returns synchronized items.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ok"
 *                 items:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       date:
 *                         type: string
 *                         description: The date of the measurement in YYYY-MM-DD format.
 *                         example: "2024-11-10"
 *                       data:
 *                         type: object
 *                         description: Data associated with the measurement, including temperature.
 *                         properties:
 *                           temperature:
 *                             type: object
 *                             properties:
 *                               value:
 *                                 type: number
 *                                 example: 36.7
 *                               source:
 *                                 type: string
 *                                 example: "tempdrop"
 *                       values:
 *                         type: array
 *                         items:
 *                           type: number
 *                         description: Historical values for the date.
 *                         example: [36.5, 36.6]
 *                       lastMeasurementTime:
 *                         type: string
 *                         format: date-time
 *                         description: Timestamp of the last measurement.
 *                         example: "2024-11-10T08:30:00Z"
 *       400:
 *         description: Error - Missing required fields.
 *       401:
 *         description: Error - Unauthorized access.
 *       500:
 *         description: Error - Server error during synchronization.
 */
router.post('/synchornize', synchronize); //legacy
/**
 * @swagger
 * /externalConnection/remove:
 *   post:
 *     summary: Remove an external connection
 *     description: Deletes a specified external connection for the authenticated user.
 *     tags:
 *       - ExternalConnection


 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
*     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: The ID of the external connection to be removed.
 *                 example: "connection12345"
 *             required:
 *               - id
 *     responses:
 *       200:
 *         description: Successfully removed the external connection.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ok"
 *       400:
 *         description: Missing required fields in the request.
 *       404:
 *         description: External connection does not exist.
 *       500:
 *         description: Error - Server error during removal.
 */
router.post('/remove', remove);

export default router;
