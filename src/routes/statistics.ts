import User from '@/models/user';
import userService from '@/services/user_service';
import express, { Request, Response } from 'express';

const router = express.Router();

const statistics = async (req: Request, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string;
    const user = await User.getUserFromToken(token);

    if (user.role != 'admin' && user.role != 'support' && user.role != 'readonly') {
      res.json({ success: false, message: 'Unauthorized' });
      return;
    }

    res.json(await userService.getStatistics());
    return;
  } catch (error) {
    console.log('Stats error: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

/**
 * @swagger
 * /statistics:
 *   post:
 *     summary: Retrieve user statistics
 *     description: Fetches a set of statistical data on users based on device platform, age range, tracking purpose, and engagement metrics.
 *     tags:
 *       - Statistics
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     responses:
 *       200:
 *         description: Successfully retrieved user statistics.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalRegistered:
 *                       type: integer
 *                       example: 1000
 *                     usersByPlatform:
 *                       type: object
 *                       properties:
 *                         ios:
 *                           type: integer
 *                           example: 600
 *                         android:
 *                           type: integer
 *                           example: 300
 *                         others:
 *                           type: integer
 *                           example: 100
 *                     usersByAge:
 *                       type: object
 *                       properties:
 *                         users_18_24:
 *                           type: integer
 *                           example: 200
 *                         users_25_34:
 *                           type: integer
 *                           example: 300
 *                         users_35_44:
 *                           type: integer
 *                           example: 150
 *                         users_45_54:
 *                           type: integer
 *                           example: 100
 *                         users_55_64:
 *                           type: integer
 *                           example: 50
 *                         users_65:
 *                           type: integer
 *                           example: 20
 *                     usersByPurpose:
 *                       type: object
 *                       properties:
 *                         trying_to_conceive:
 *                           type: integer
 *                           example: 400
 *                         cycle_info_tracking_fertility:
 *                           type: integer
 *                           example: 300
 *                         not_sure_yet:
 *                           type: integer
 *                           example: 100
 *                     totalWithMoreThanOneMeasurement:
 *                       type: integer
 *                       example: 200
 *                     totalWithMoreThanTwoExercises:
 *                       type: integer
 *                       example: 150
 *       401:
 *         description: Unauthorized access due to insufficient permissions.
 *       500:
 *         description: Internal server error during data retrieval.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Stats error: {error message}"
 */
router.post('/', statistics);

export default router;
