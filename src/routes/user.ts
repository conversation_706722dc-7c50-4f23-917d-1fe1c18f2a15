import { Consts } from '@/consts';
import User from '@/models/user';
import userService from '@/services/user_service';
import express, { Request, Response } from 'express';
import fs from 'fs';
import Mixpanel from 'mixpanel';

const router = express.Router();

let mixpanelApi: Mixpanel.Mixpanel | null = null;

if (Consts.environment == 'live') {
  mixpanelApi = Mixpanel.init(Consts.mixPanelToken);
}

const createHTML = (body: string): string => {
  const styles = `
    * {
      font-family: sans-serif;
      font-size: 13pt;
    }
    .button {
      background-color: #04BEA5;
      font-weight: 700;
      border-radius: 15px;
      color: #efefef;
      border: none;
      text-align: center;
      padding: 10px 20px;
    }
    .inputText {
      padding: 10px;
    }
  `;

  return `
    <html>
      <head>
        <style>${styles}</style>
      </head>
      <body>
        <center>
          <br/><br/><br/>
          <img src="/logo.png" alt="Logo" />
          <br/><br/><br/>
          ${body}
        </center>
      </body>
    </html>
  `;
};

const list = async (req: Request, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string | undefined;
    if (!token) {
      res.json({ success: false, message: 'Token is missing' });
      return;
    }

    const user = await User.getUserFromToken(token);

    const users = await userService.getUsers({ user, body: req.body });

    const pagination = {
      page: req.body.page || 0,
      pagesize: req.body.pagesize || 20,
      total: 1000,
    };

    res.json({ success: true, message: 'Ok', pagination, data: users });
    return;
  } catch (error) {
    console.log('User list fail: ' + error);
    res.status(500).json({ success: false, message: error });
    return;
  }
};

const signUp = async (req: Request, res: Response) => {
  try {
    if (req.body.email == null || req.body.password == null) {
      res.json({ success: false, message: 'Missing fields' });
      return;
    }

    res.json(await userService.signUp(req.body));
    return;
  } catch (error) {
    console.log('User create error: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const login = async (req: Request, res: Response) => {
  try {
    const { email, password, token } = req.body;

    if (!email || !password) {
      res.json({ success: false, message: 'Missing fields' });
      return;
    }

    const result = await userService.login(email, password, token);

    res.json(result);
  } catch (error) {
    console.error('User login error:', error);
    res.json({ success: false, message: 'Could not authenticate' });
  }
};

const verify = async (req: Request, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string | undefined;
    if (!token) {
      res.json({ success: false, message: 'Token is missing' });
      return;
    }

    let { language, appVersion, deviceInfo, lastKeggFirmwareVersion } = req.body;

    if (req.headers['x-application-version']) {
      appVersion = req.headers['x-application-version'] as string;
    }

    res.json(await userService.verifyUser(token, language, appVersion, deviceInfo, lastKeggFirmwareVersion));
  } catch (error) {
    console.error('User verify error:', (error as Error).message);
    res.json({ success: false, message: (error as Error).message });
  }
};

const create = async function (req: Request, res: Response) {
  try {
    const token = req.headers['x-access-token'] as string | undefined;
    if (!token) {
      res.json({ success: false, message: 'Token is missing' });
      return;
    }

    res.json(await userService.createUser(req.body));
    return;
  } catch (error) {
    console.log('User create error: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const update = async (req: Request, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string | undefined;
    if (!token) {
      res.json({ success: false, message: 'Token is missing' });
      return;
    }

    res.json(await userService.updateUser(token, req.body, mixpanelApi));
    return;
  } catch (error) {
    console.error('User update fail: ' + error);
    res.json({ success: false, message: 'An error occurred while updating the user.' });
  }
};

const changePassword = async function (req: Request, res: Response) {
  try {
    const token = req.headers['x-access-token'] as string | undefined;
    if (!token) {
      res.json({ success: false, message: 'Token is missing' });
      return;
    }
    res.json(await userService.changePassword(token, req.body.password));
    return;
  } catch (error) {
    console.log('User change password fail: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const remove = async function (req: Request, res: Response) {
  try {
    const token = req.headers['x-access-token'] as string | undefined;
    if (!token) {
      res.json({ success: false, message: 'Token is missing' });
      return;
    }

    res.json(await userService.removeUser(token, req.body.id));
    return;
  } catch (error) {
    console.log('User remove fail: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const copy = async function (req: Request, res: Response) {
  try {
    const token = req.headers['x-access-token'] as string | undefined;
    if (!token) {
      res.json({ success: false, message: 'Token is missing' });
      return;
    }
    res.json(await userService.copyUser(token, req.body));
    return;
  } catch (error) {
    console.log('User copy fail: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const exp = async function (req: Request, res: Response) {
  try {
    res.setHeader('Content-Type', 'text/csv');

    const token = req.headers['x-access-token'] as string | undefined;
    if (!token) {
      res.json({ success: false, message: 'Token is missing' });
      return;
    }

    res.json(await userService.exportUser(token, req.body.columns));
    return;
  } catch (error) {
    console.log('User export fail: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const sendToken = async function (req: Request, res: Response) {
  try {
    const { email, password, token } = req.body;
    if (!email || !password) {
      res.json({ success: false, message: 'Missing fields' });
      return;
    }

    res.json(await userService.sendToken(email, password, token));
    return;
  } catch (error) {
    console.log('User sendToken fail: ' + error);
    res.json({ success: false, message: 'Could not authenticate' });
    return;
  }
};

const verifyEmail = async function (req: Request, res: Response) {
  const token = req.headers['x-access-token'] as string | undefined;
  if (!token) {
    res.json({ success: false, message: 'Token is missing' });
    return;
  }
  res.json(await userService.verifyEmail(token));
  return;

  // try {
  //   const token = req.headers['x-access-token'] as string | undefined;
  //   if (!token) {
  //     res.json({ success: false, message: 'Token is missing' });
  //     return;
  //   }
  //  res.json(await userService.verifyEmail(req.query.email));
  //  return;
  // } catch (error) {
  //   console.log('User verify email error: ' + error);
  //   res.json({ success: false, message: error });
  //   return;
  // }
};

const confirmEmail = async function (req: Request, res: Response) {
  try {
    if (req.query.email == null) {
      res.json({ success: false, message: 'Missing fields' });
      return;
    }

    res.json(await userService.confirmEmail(req.query.email as string));
    return;
  } catch (error) {
    console.log('User confirm email fail: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const predictions = async function (req: Request, res: Response) {
  try {
    const token = req.headers['x-access-token'] as string | undefined;
    if (!token) {
      res.json({ success: false, message: 'Token is missing' });
      return;
    }

    const { confirmation, prediction } = await userService.getPredictions(token, req.body.user);
    res.json({ success: true, confirmation, prediction });
  } catch (error) {
    console.log('User predictions fail: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const sendResetPasswordLink = async function (req: Request, res: Response) {
  try {
    res.json(userService.sendResetPasswordLink(req.body.email));
    return;
  } catch (error) {
    console.log('User send reset password fail: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const resetPasswordForm = async (req: Request, res: Response) => {
  try {
    const { email, token } = req.query;

    const body = `
      <script>
        function validateForm() {
          const password = document.forms["form1"]["password"].value;
          if (password.length < 8) {
            alert("Minimum password length is 8");
            return false;
          }
        }
      </script>
      <form action="/user/resetPassword" method="post" name="form1" onsubmit="return validateForm()">
        <input type="hidden" id="email" name="email" value="${email}">
        <input type="hidden" id="token" name="token" value="${token}">
        <p>Enter new password</p>
        <input class="inputText" type="text" id="password" name="password">
        <br/><br/>
        <input class="button" type="submit" value="Change password">
      </form>
    `;

    res.send(createHTML(body));
  } catch (error) {
    console.log('Error resetting password:', error);
    res.send(createHTML('Could not change password'));
  }
};

const resetPassword = async function (req: Request, res: Response) {
  const { email, password, token } = req.body;

  if (!email || !password || !token) {
    res.send('Missing fields');
    return;
  }

  try {
    const result = await userService.resetPassword(email, token, password);

    if (result.success) {
      res.send(createHTML(result.message));
      return;
    }
    res.send(createHTML(result.message));
    return;
  } catch (error) {
    console.log('Error resetting password:', error);
    res.send(createHTML('Could not change password'));
    return;
  }
};

const test = async (req: Request, res: Response) => {
  try {
    const tokenDirectory = 'config/token';
    const keypr = fs.readFileSync(tokenDirectory + '/private.key').toString();
    const keypu = fs.readFileSync(tokenDirectory + '/public.key').toString();

    res.send(createHTML(keypr + keypu));
  } catch (error) {
    console.error('Error fetching keys:', error);
    res.send(createHTML('Error fetching keys'));
  }
};

const test2 = async function (req: Request, res: Response) {
  try {
    res.send(createHTML(JSON.stringify(Consts)));
  } catch (error) {
    console.error('Error fetching keys:', error);
    res.send(createHTML('Error fetching keys'));
  }
};

//Auth V2

const requestLoginLink = async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.body.email) {
      res.status(400).json({ success: false, message: 'Email required' });
      return;
    }

    //Hello Apple review testing
    if(req.body.email.toLowerCase() == "<EMAIL>") {
      const user = await User.findOne({
        email: "<EMAIL>"
      });

      if (!user){
        res.status(400).json({ success: false, message: 'App Store review testing user not found' });
        return;
      }

      const token = await User.makeToken(user._id.toString());

      res.json({ success: true, storeReviewTesting: true, token: token });
      return;
    }
    //-----------------------
    const result = await userService.requestLoginLink(req.body.email.toLowerCase());
    res.json(result);
  } catch (error) {
    console.error('Request Login Link Error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};

const signUpV2 = async (req: Request, res: Response) => {
  try {
    const result = await userService.signUpV2(req.body);
    res.json(result);
  } catch (error) {
    console.error('Signup V2 Error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};

const loginV2 = async (req: Request, res: Response) => {
  try {
    const result = await userService.loginV2(req.body);
    res.json(result);
  } catch (error) {
    console.error('Login V2 Error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};

const getUserV2Handler = async (req: Request, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string | undefined;
    if (!token) {
      res.json({ success: false, message: 'Token is missing' });
      return;
    }
    const result = await userService.getUserV2(token);
    res.json(result);
  } catch (error) {
    console.error('getUserV2 error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};

const updateUserV2Handler = async (req: Request, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string | undefined;
    if (!token) {
      res.json({ success: false, message: 'Token is missing' });
      return;
    }
    const result = await userService.updateUserV2(token, req.body, mixpanelApi);
    res.json(result);
  } catch (error) {
    console.error('updateUserV2 error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};


const klaviyoEvent = async (req: Request, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string | undefined;
    if (!token) {
      res.status(401).json({ success: false, message: 'Token is missing' });
      return;
    }

    const { eventAction, eventName, userProperties } = req.body;

    if (!eventAction || !eventName) {
      res.status(400).json({ success: false, message: 'Missing fields' });
      return;
    }

    const result = await userService.protectedKlaviyoSendEvent(token, eventAction, eventName, userProperties || null);
    res.json({ success: true, message: 'Event sent successfully', data: result });
  } catch (error) {
    console.error('Klaviyo event error:', error);
    res.status(500).json({ success: false, message: 'Error sending event' });
  }
};


/**
 * @swagger
 * /user:
 *   post:
 *     summary: List users with various filters
 *     description: Fetches a paginated list of users with optional filters such as email, name, tracking reason, pregnancy status, app version, firmware, and serial. Only authorized roles (admin, support, readonly) can apply filters; other users can only query their own data.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: ID of a specific user.
 *               email:
 *                 type: string
 *                 description: Email filter for the user search.
 *               name:
 *                 type: string
 *                 description: Name filter for first or last name.
 *               trackingReason:
 *                 type: string
 *                 description: User's tracking reason.
 *               pregnancy:
 *                 type: boolean
 *                 description: Filter for users with pregnancy status.
 *               appVersion:
 *                 type: string
 *                 description: Filter by app version.
 *               firmware:
 *                 type: string
 *                 description: Filter by firmware version.
 *               serial:
 *                 type: string
 *                 description: Filter by Kegg device serial.
 *               page:
 *                 type: integer
 *                 description: Page number for pagination.
 *                 default: 0
 *               pagesize:
 *                 type: integer
 *                 description: Number of users per page.
 *                 default: 20
 *               sortBy:
 *                 type: string
 *                 description: Field to sort the results by.
 *                 default: "created"
 *               sort:
 *                 type: integer
 *                 description: Sort order, 1 for ascending and -1 for descending.
 *                 default: -1
 *     responses:
 *       200:
 *         description: Successfully retrieved user list.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ok"
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                       example: 0
 *                     pagesize:
 *                       type: integer
 *                       example: 20
 *                     total:
 *                       type: integer
 *                       example: 1000
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         description: User ID.
 *                       email:
 *                         type: string
 *                         description: User's email.
 *                       phone:
 *                         type: string
 *                         description: User's phone number.
 *                       group:
 *                         type: string
 *                         description: User's group.
 *                       measurementsCount:
 *                         type: integer
 *                         description: Count of measurements for the user.
 *                       calendarCount:
 *                         type: integer
 *                         description: Count of calendar entries for the user.
 *                       enabled:
 *                         type: boolean
 *                       test:
 *                         type: boolean
 *                       created:
 *                         type: string
 *                         format: date-time
 *                       lastActivity:
 *                         type: string
 *                         format: date-time
 *                       role:
 *                         type: string
 *                       info:
 *                         type: object
 *                       cycleCount:
 *                         type: integer
 *       400:
 *         description: Missing token or unauthorized access.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Token is missing"
 *       500:
 *         description: Server error while fetching user list.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User list fail: [Error details]"
 */
router.post('/', list);
/**
 * @swagger
 * /user/signup:
 *   post:
 *     summary: Sign up a new user
 *     description: Creates a new user with the provided email and password. Optionally, sets email verification status and user language.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User's email address.
 *               password:
 *                 type: string
 *                 format: password
 *                 description: User's password.
 *               language:
 *                 type: string
 *                 description: Preferred language of the user.
 *               needToVerifyEmail:
 *                 type: boolean
 *                 description: Indicates if the user needs to verify their email.
 *     responses:
 *       200:
 *         description: Successfully created a new user.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 token:
 *                   type: string
 *                   description: Authentication token for the new user.
 *                 message:
 *                   type: string
 *                   example: "Signup successful"
 *                 user:
 *                   type: object
 *                   properties:
 *                     _id:
 *                       type: string
 *                       description: User ID of the newly created user.
 *                     email:
 *                       type: string
 *                       description: User's email address.
 *                     info:
 *                       type: object
 *                       description: Additional information about the user.
 *       400:
 *         description: Missing fields in the signup request.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Missing fields"
 *       409:
 *         description: Email already exists.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Email already exists"
 *                 errorCode:
 *                   type: integer
 *                   example: 1
 *       500:
 *         description: Internal server error during user creation.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User create error: [Error details]"
 */
router.post('/signup', signUp);
/**
 * @swagger
 * /user/login:
 *   post:
 *     summary: Log in a user
 *     description: Authenticates a user with email and password. For admin, support, or readonly roles, an SMS token may be required.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User's email address.
 *               password:
 *                 type: string
 *                 format: password
 *                 description: User's password.
 *               token:
 *                 type: string
 *                 description: SMS token for users in admin, support, or readonly roles.
 *     responses:
 *       200:
 *         description: Successfully authenticated the user.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 token:
 *                   type: string
 *                   description: Authentication token for the user.
 *                 message:
 *                   type: string
 *                   example: "Authentication successful"
 *                 user:
 *                   type: object
 *                   properties:
 *                     _id:
 *                       type: string
 *                       description: User ID of the authenticated user.
 *                     email:
 *                       type: string
 *                       description: User's email address.
 *                     info:
 *                       type: object
 *                       description: Additional information about the user.
 *       400:
 *         description: Missing fields in the login request.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Missing fields"
 *       401:
 *         description: Authentication failed due to invalid credentials or token.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid token"
 *       403:
 *         description: Account disabled, preventing login.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Account disabled"
 *       500:
 *         description: Internal server error during authentication.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Could not authenticate"
 */
router.post('/login', login);
/**
 * @swagger
 * /user/verify:
 *   post:
 *     summary: Verify a user's token and update their information
 *     description: Verifies the provided token, updates user activity, and optionally updates language, app version, device info, and firmware version.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               language:
 *                 type: string
 *                 description: Language preference of the user.
 *               appVersion:
 *                 type: string
 *                 description: Version of the app the user is using.
 *               deviceInfo:
 *                 type: string
 *                 description: Information about the user's device.
 *               lastKeggFirmwareVersion:
 *                 type: string
 *                 description: Last known firmware version of the user's device.
 *     responses:
 *       200:
 *         description: Successfully verified and updated user information.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ok"
 *                 info:
 *                   type: object
 *                   description: Updated user information.
 *                   properties:
 *                     language:
 *                       type: string
 *                       example: "en"
 *                     appVersion:
 *                       type: string
 *                       example: "1.2.3"
 *                     deviceInfo:
 *                       type: string
 *                       example: "iPhone 12"
 *                     lastKeggFirmwareVersion:
 *                       type: string
 *                       example: "1.0.1"
 *                 userId:
 *                   type: string
 *                   description: The ID of the verified user.
 *                   example: "507f191e810c19729de860ea"
 *                 onboard:
 *                   type: boolean
 *                   description: Indicates if the user has completed onboarding.
 *                   example: true
 *                 created:
 *                   type: string
 *                   format: date-time
 *                   description: Date and time the user was created.
 *                   example: "2023-01-01T00:00:00.000Z"
 *       400:
 *         description: Missing or invalid token.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Token is missing"
 *       403:
 *         description: Account is disabled, preventing verification.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Account disabled"
 *       500:
 *         description: Internal server error during verification.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Error message"
 */
router.post('/verify', verify);
/**
 * @swagger
 * /user/create:
 *   post:
 *     summary: Create a new user
 *     description: Creates a new user with the provided details if authenticated by a valid token.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *               - role
 *             properties:
 *               email:
 *                 type: string
 *                 description: Email of the new user.
 *               password:
 *                 type: string
 *                 description: Password for the new user.
 *               role:
 *                 type: string
 *                 enum: [admin, support, readonly, user]
 *                 description: Role of the new user.
 *               enabled:
 *                 type: boolean
 *                 description: Status of the user's account.
 *               test:
 *                 type: boolean
 *                 description: Indicates if the account is for testing.
 *               phone:
 *                 type: string
 *                 description: Phone number of the user.
 *               group:
 *                 type: string
 *                 description: Group the user belongs to.
 *               firstName:
 *                 type: string
 *                 description: First name of the user.
 *               lastName:
 *                 type: string
 *                 description: Last name of the user.
 *               note:
 *                 type: string
 *                 description: Additional note for the user.
 *               keggIdentifiers:
 *                 type: string
 *                 description: KEGG device identifiers.
 *               lastUnfinishedCycleStart:
 *                 type: string
 *                 format: date-time
 *                 description: Start date of the last unfinished cycle.
 *               lastFinishedCycleLength:
 *                 type: number
 *                 description: Length of the last completed cycle.
 *               initialCycleLength:
 *                 oneOf:
 *                   - type: number
 *                   - type: string
 *                     enum: [not_sure]
 *                 description: Initial cycle length or 'not_sure' if unknown.
 *     responses:
 *       200:
 *         description: Successfully created the user.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ok"
 *                 data:
 *                   type: object
 *                   description: Details of the created user.
 *       400:
 *         description: Missing required fields for user creation.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Missing fields:<br>email, password"
 *       401:
 *         description: Invalid or missing token.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Token is missing"
 *       500:
 *         description: Internal server error during user creation.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Error message"
 */
router.post('/create', create);
/**
 * @swagger
 * /user/update:
 *   post:
 *     summary: Update an existing user
 *     description: Updates user information if authenticated with a valid token. Admins and support roles can update any user by ID; others can only update their own profile.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: ID of the user to update. Required for admin and support roles; ignored for regular users.
 *               email:
 *                 type: string
 *                 description: New email for the user.
 *               password:
 *                 type: string
 *                 description: New password for the user.
 *               role:
 *                 type: string
 *                 enum: [admin, support, readonly, user]
 *                 description: New role for the user. Only admins and support roles can set this.
 *               enabled:
 *                 type: boolean
 *                 description: Account enabled status.
 *               phone:
 *                 type: string
 *                 description: Phone number of the user.
 *               group:
 *                 type: string
 *                 description: Group the user belongs to.
 *               firstName:
 *                 type: string
 *                 description: First name of the user.
 *               lastName:
 *                 type: string
 *                 description: Last name of the user.
 *               note:
 *                 type: string
 *                 description: Note for the user.
 *               keggIdentifiers:
 *                 type: string
 *                 description: KEGG device identifiers.
 *               lastUnfinishedCycleStart:
 *                 type: string
 *                 format: date-time
 *                 description: Start date of the last unfinished cycle.
 *               lastFinishedCycleLength:
 *                 type: number
 *                 description: Length of the last completed cycle.
 *               initialCycleLength:
 *                 oneOf:
 *                   - type: number
 *                   - type: string
 *                     enum: [not_sure]
 *                 description: Initial cycle length or 'not_sure' if unknown.
 *               trackingReason:
 *                 type: string
 *                 enum: [trying_to_conceive, cycle_info_tracking_fertility, not_sure_yet]
 *                 description: User's tracking reason.
 *               regularCycle:
 *                 oneOf:
 *                   - type: boolean
 *                   - type: string
 *                     enum: [not_specified]
 *                 description: Whether the user has a regular cycle.
 *               tryingToConceiveDuration:
 *                 type: string
 *                 description: Duration of trying to conceive.
 *               height:
 *                 type: number
 *                 description: Height of the user.
 *               weight:
 *                 type: number
 *                 description: Weight of the user.
 *               supplements:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: List of supplements.
 *               consent:
 *                 type: object
 *                 properties:
 *                   research:
 *                     type: boolean
 *                     description: Consent for research purposes.
 *                   keggPlus:
 *                    type: boolean
 *                   description: Consent for Kegg Plus.
 *     responses:
 *       200:
 *         description: Successfully updated the user profile.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ok"
 *                 profile:
 *                   type: object
 *                   description: Updated profile details of the user.
 *       400:
 *         description: User does not exist.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User does not exist"
 *       401:
 *         description: Invalid or missing token.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Token is missing"
 *       500:
 *         description: Internal server error during user update.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Error message"
 */
router.post('/update', update);
/**
 * @swagger
 * /user/changePassword:
 *   post:
 *     summary: Change the user password
 *     description: Updates the password for the authenticated user. Requires the user's current access token.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               password:
 *                 type: string
 *                 description: New password for the user.
 *                 example: "newPassword123"
 *             required:
 *               - password
 *     responses:
 *       200:
 *         description: Successfully changed the user password.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Changed"
 *       400:
 *         description: Missing fields, including the new password.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Missing fields"
 *       401:
 *         description: Invalid or missing token.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Token is missing"
 *       500:
 *         description: Internal server error during password change.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Error message"
 */
router.post('/changePassword', changePassword);
/**
 * @swagger
 * /user/remove:
 *   post:
 *     summary: Remove a user and associated data
 *     description: Deletes a user along with all associated measurements, calendar entries, and other data. Requires the user's access token.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: The ID of the user to be removed (optional for admin users).
 *                 example: "12345abcde"
 *             required:
 *               - id
 *     responses:
 *       200:
 *         description: Successfully removed the user and their associated data.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Deleted"
 *       400:
 *         description: Missing required token or ID.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Token is missing"
 *       401:
 *         description: Unauthorized request if the user is not an admin and the ID is not their own.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Unauthorized"
 *       500:
 *         description: Internal server error during the removal process.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Error message"
 */
router.post('/remove', remove);
/**
 * @swagger
 * /user/copy:
 *   post:
 *     summary: Copy a user
 *     description: Copies a user's data (measurements, calendar, etc.) and creates a new user with the provided email, password, and other information. This operation requires an admin role.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: The ID of the user to copy.
 *                 example: "60c72b2f9b1e8e31a1e58767"
 *               email:
 *                 type: string
 *                 description: The email for the new user.
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 description: The password for the new user.
 *                 example: "newuserpassword"
 *               firstName:
 *                 type: string
 *                 description: The first name of the new user.
 *                 example: "John"
 *               lastName:
 *                 type: string
 *                 description: The last name of the new user.
 *                 example: "Doe"
 *             required:
 *               - id
 *               - email
 *               - password
 *               - firstName
 *               - lastName
 *     responses:
 *       200:
 *         description: Successfully copied the user and associated data.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User copied"
 *                 data:
 *                   type: object
 *                   description: The newly created user.
 *       400:
 *         description: Missing required fields or invalid data.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Missing fields"
 *       401:
 *         description: Unauthorized request (only admins can copy users).
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Unauthorized"
 *       409:
 *         description: Email already exists.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Email already exists"
 *       500:
 *         description: Internal server error during the copy process.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Error message"
 */
router.post('/copy', copy);
/**
 * @swagger
 * /user/export:
 *   post:
 *     summary: Export user data to CSV
 *     description: Exports a list of user data based on selected columns to a CSV format. Only accessible by users with the roles of 'admin', 'support', or 'readonly'.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               columns:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: |
 *                   List of columns to be exported. The list can include any of the following: 'id', 'email', 'firstName', 'lastName', 'serial', 'created', 'lastActivity', 'measurements', 'cycles', 'diary', and others related to the user's profile, calendar, and device information.
 *                 example:
 *                   - "id"
 *                   - "email"
 *                   - "firstName"
 *                   - "lastName"
 *                   - "dateOfBirth"
 *     responses:
 *       200:
 *         description: Successfully exported user data as CSV.
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               example: |
 *                 id,email,firstName,lastName,created,lastActivity
 *                 12345,<EMAIL>,John,Doe,2022-01-01 12:00:00,2023-11-12 15:30:00
 *       400:
 *         description: Missing required fields or invalid columns specified.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Missing columns"
 *       401:
 *         description: Unauthorized access due to insufficient role permissions.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Unauthorized"
 *       500:
 *         description: Internal server error during the export process.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Error during export"
 */
router.post('/export', exp);
/**
 * @swagger
 * /user/sendToken:
 *   post:
 *     summary: Send a verification token to the user
 *     description: Authenticates the user with email and password, then sends a verification token via SMS if the user has a phone number. If no phone number is available, it proceeds with a standard login.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 description: The user's email address.
 *               password:
 *                 type: string
 *                 description: The user's password.
 *             required:
 *               - email
 *               - password
 *             example:
 *               email: "<EMAIL>"
 *               password: "password123"
 *     responses:
 *       200:
 *         description: Successfully authenticated, token sent by SMS if phone number is available.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *       400:
 *         description: Missing email or password in the request.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Missing fields"
 *       401:
 *         description: Authentication failed due to invalid email or password.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Could not authenticate"
 *       500:
 *         description: Internal server error during the process.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Could not authenticate"
 */
router.post('/sendToken', sendToken);
/**
 * @swagger
 * /user/verifyEmail:
 *   post:
 *     summary: Verify the user's email status
 *     description: Checks if the user's email is verified. Responds with success or failure depending on the email verification status.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     responses:
 *       200:
 *         description: Successfully verified the user's email status.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ok"
 *       400:
 *         description: Token is missing or invalid, or the email is not verified.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example:
 *                     - "Token is missing"
 *                     - "Email not verified"
 *       500:
 *         description: Internal server error during the email verification process.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Error"
 */
router.post('/verifyEmail', verifyEmail);
/**
 * @swagger
 * /user/confirmEmail:
 *   get:
 *     summary: Confirm a user's email
 *     description: Confirms the email address of the user by setting the `emailVerified` status to `true`. Requires the email as a query parameter.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     responses:
 *       200:
 *         description: Successfully confirmed the user's email.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ok"
 *       400:
 *         description: Missing email query parameter.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Missing fields"
 *       404:
 *         description: User with the given email does not exist.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User does not exist"
 *       500:
 *         description: Internal server error while processing the email confirmation.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Error"
 */
router.get('/confirmEmail', confirmEmail);
/**
 * @swagger
 * /user/predictions:
 *   post:
 *     summary: Get ovulation confirmation and predictions for a user
 *     description: >-
 *       Sends requests to an external service to get ovulation confirmation and
 *       predictions for a specific user, and returns the results. Requires the
 *       user ID and a valid authentication token.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user:
 *                 type: string
 *                 description: The ID of the user whose predictions are needed.
 *                 example: user123
 *     responses:
 *       '200':
 *         description: Successfully fetched predictions and ovulation confirmation.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 confirmation:
 *                   type: object
 *                   description: The confirmation data returned by the external ovulation service.
 *                 prediction:
 *                   type: object
 *                   description: The prediction data returned by the external prediction service.
 *       '400':
 *         description: Missing token or user ID in the request.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Token is missing"
 *       '401':
 *         description: Unauthorized access due to insufficient user role.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Unauthorized"
 *       '500':
 *         description: Internal server error while fetching predictions or confirmation data.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Error"
 */
router.post('/predictions', predictions);
/**
 * @swagger
 * /user/sendResetPasswordLink:
 *   post:
 *     summary: Send a password reset link to the user's email
 *     description: Sends a password reset link to the user's email if the provided email exists in the system. The link will be valid for 1 hour.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: The user's email address.
 *                 example: "<EMAIL>"
 *     responses:
 *       200:
 *         description: A reset password link was successfully sent to the user's email.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *       400:
 *         description: Missing email in the request body.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Missing fields"
 *       404:
 *         description: The provided email does not exist in the system.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *       500:
 *         description: Internal server error while trying to send the reset password email.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Error"
 */
router.post('/sendResetPasswordLink', sendResetPasswordLink);
/**
 * @swagger
 * /user/resetPassword:
 *   get:
 *     summary: Serve the password reset form
 *     description: Renders a password reset form where the user can enter a new password. Requires the `email` and `token` as query parameters.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *       - in: query
 *         name: email
 *         required: true
 *         description: The user's email address for password reset.
 *         schema:
 *           type: string
 *           format: email
 *           example: "<EMAIL>"
 *       - in: query
 *         name: token
 *         required: true
 *         description: The temporary token sent to the user's email for password reset.
 *         schema:
 *           type: string
 *           example: "abcdef123456"
 *     responses:
 *       200:
 *         description: Successfully served the password reset form.
 *         content:
 *           text/html:
 *             schema:
 *               type: string
 *               example: "<html><body><form>...</form></body></html>"
 *       400:
 *         description: Missing `email` or `token` in the query parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Missing fields"
 */
router.get('/resetPassword', resetPasswordForm);
/**
 * @swagger
 * /user/resetPassword:
 *   post:
 *     summary: Reset the user's password
 *     description: Resets the user's password if the provided `email`, `token`, and `password` are valid. The token should be within the validity period.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: The user's email address.
 *                 example: <EMAIL>
 *               token:
 *                 type: string
 *                 description: >-
 *                   The temporary token sent to the user's email for password
 *                   reset.
 *                 example: abcdef123456
 *               password:
 *                 type: string
 *                 description: The new password to set for the user.
 *                 example: newpassword123
 *     responses:
 *       200:
 *         description: Successfully changed the user's password.
 *         content:
 *           text/html:
 *             schema:
 *               type: string
 *               example: "<html><body>Password changed successfully.</body></html>"
 *       400:
 *         description: Missing fields or invalid password.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Missing fields or invalid password."
 *       404:
 *         description: User not found or token expired.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User not found or token expired."
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Could not change password"
 */
router.post('/resetPassword', resetPassword);
/**
 * @swagger
 * /user/test:
 *   get:
 *     summary: Retrieve combined private and public key content
 *     description: Reads private and public key files from the token directory and returns the combined content as HTML.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     responses:
 *       200:
 *         description: Successfully retrieved key content in HTML.
 *         content:
 *           text/html:
 *             schema:
 *               type: string
 *               example: "<html>...</html>"
 *       500:
 *         description: Error reading key files or generating HTML content.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Error message explaining the failure"
 */
router.get('/test', test);
/**
 * @swagger
 * /user/test2:
 *   get:
 *     summary: Retrieve constant values as HTML
 *     description: Returns constant values in HTML format using a JSON stringified representation.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     responses:
 *       200:
 *         description: Successfully retrieved constant values in HTML format.
 *         content:
 *           text/html:
 *             schema:
 *               type: string
 *               example: "<html>...</html>"
 *       500:
 *         description: Error generating HTML content from constants.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Error message explaining the failure"
 */
router.get('/test2', test2);

/**
 * @swagger
 * /user/requestLoginLink:
 *   post:
 *     summary: Request a magic login link via email
 *     description: >
 *       Generates and sends a magic login link to the specified email address. The link is valid for 1 hour.
 *       For Apple App Store review testing, using the email "<EMAIL>" will return a token directly
 *       without sending an email, allowing immediate authentication for testing purposes.
 *     tags:
 *       - Users
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: >
 *                   The user's email address to send the login link.
 *                   Special case: Using "<EMAIL>" will return a token directly for App Store review testing.
 *                 example: "<EMAIL>"
 *     responses:
 *       200:
 *         description: Successfully sent the login link or returned a token for testing.
 *         content:
 *           application/json:
 *             schema:
 *               oneOf:
 *                 - type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *                       example: true
 *                     message:
 *                       type: string
 *                       example: "Login link sent to your email"
 *                 - type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *                       example: true
 *                     storeReviewTesting:
 *                       type: boolean
 *                       example: true
 *                     token:
 *                       type: string
 *                       example: "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
 */
router.post('/requestLoginLink', requestLoginLink);

/**
 * @swagger
 * /user/signupV2:
 *   post:
 *     summary: Sign up a new user (with magic link or Apple/Google SSO)
 *     description: Creates a new user using either email (magic link) or external authorization methods like Google or Apple.
 *     tags:
 *       - Users
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               authorizationType:
 *                 type: string
 *                 enum: [email, google, apple]
 *                 description: The authorization type for signup.
 *               identityToken:
 *                 type: string
 *                 description: Either the user's email (for email login) or an identity token for SSO.
 *               language:
 *                 type: string
 *                 description: Preferred language of the user.
 *             required:
 *               - authorizationType
 *               - identityToken
 *     responses:
 *       200:
 *         description: Successfully signed up the user.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Check your email for login link"
 *                 token:
 *                   type: string
 *                   description: The authentication token for the new user.
 *                 user:
 *                   type: object
 *                   description: User details.
 *                 freshCreated:
 *                   type: boolean
 *                   description: Indicates if the user was freshly created.
 *       400:
 *         description: Missing required fields or invalid input.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Missing fields"
 *       409:
 *         description: User already exists.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User already exists"
 *       500:
 *         description: Internal server error during signup.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Authentication error"
 */
router.post('/signupV2', signUpV2);

/**
 * @swagger
 * /user/loginV2:
 *   post:
 *     summary: Log in a user (with magic link or Apple/Google SSO)
 *     description: Authenticates a user through either email-based login link or external services like Google and Apple.
 *     tags:
 *       - Users
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               authorizationType:
 *                 type: string
 *                 enum: [email, google, apple]
 *                 description: The type of login.
 *               identityToken:
 *                 type: string
 *                 description: The token for authentication (magic link token or SSO identity token).
 *               language:
 *                 type: string
 *                 description: Preferred language of the user.
 *             required:
 *               - authorizationType
 *               - identityToken
 *     responses:
 *       200:
 *         description: Successfully logged in the user.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Logged in"
 *                 token:
 *                   type: string
 *                   description: Authentication token for the user.
 *                 user:
 *                   type: object
 *                   description: User details.
 *                 freshCreated:
 *                   type: boolean
 *                   description: Indicates if the user was freshly created.
 *       400:
 *         description: Invalid or expired token.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid or expired token"
 *       401:
 *         description: Authentication failed.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Authentication failed"
 *       500:
 *         description: Server error during login.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Authentication error"
 */
router.post('/loginV2', loginV2);

/**
 * @swagger
 * /user/getUserV2:
 *   post:
 *     summary: Retrieve user data in transitional format (V2)
 *     description: Retrieves a user profile in the new transitional format for migration purposes. Requires a valid access token.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     responses:
 *       200:
 *         description: Successfully retrieved user transitional data.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User retrieved successfully"
 *                 data:
 *                   $ref: '#/components/schemas/UserDataV2'
 *       400:
 *         description: Missing or invalid token.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Token is missing"
 *       500:
 *         description: Server error retrieving user data.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Server error"
 */
router.post('/getUserV2', getUserV2Handler);

/**
 * @swagger
 * /user/updateUserV2:
 *   post:
 *     summary: Update user data in transitional format (V2)
 *     description: Updates a user profile in the new transitional format for migration purposes. Admins and support roles can update any user; others can update only their own profile. Requires a valid access token.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UserDataV2'
 *     responses:
 *       200:
 *         description: Successfully updated user transitional data.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User updated successfully"
 *                 data:
 *                   $ref: '#/components/schemas/UserDataV2'
 *       400:
 *         description: Missing or invalid token or user not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Token is missing"
 *       500:
 *         description: Server error updating user data.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Server error"
 */
router.post('/updateUserV2', updateUserV2Handler);

/**
 * @swagger
 * /user/klaviyoEvent:
 *   post:
 *     summary: Send a Klaviyo event
 *     description: Sends a custom event to Klaviyo with the specified action code, human-friendly name, and custom user properties.
 *     tags:
 *       - Users
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               eventAction:
 *                 type: string
 *                 description: The action code associated with the event.
 *                 example: "something-happened"
 *               eventName:
 *                 type: string
 *                 description: The human-friendly name of the event.
 *                 example: "Something happened with this User"
 *               userProperties:
 *                 type: object
 *                 additionalProperties:
 *                   type: string
 *                 description: A key-value object defining custom properties on Klaviyo.
 *                 example:
 *                   email: "<EMAIL>"
 *                   plan: "premium"
 *     responses:
 *       200:
 *         description: Successfully sent the Klaviyo event.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Event sent successfully"
 *       400:
 *         description: Missing or invalid fields in the request.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Missing fields"
 *       401:
 *         description: Missing or invalid token.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Token is missing"
 *       500:
 *         description: Internal server error while sending the event.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Error sending event"
 */
router.post('/klaviyoEvent', klaviyoEvent);

export default router;
