import express, { Request, Response } from 'express';

const router = express.Router();

/**
 * @swagger
 * /health:
 *   get:
 *     summary: Health check endpoint
 *     description: Returns a simple "HEALTHY" message to indicate the server is running.
 *     tags:
 *       - Health
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     responses:
 *       200:
 *         description: Server is healthy.
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: "HEALTHY"
 */
router.get('/', function (req: Request, res: Response) {
  res.send('HEALTHY');
});

export default router;
