import userService from '@/services/user_service';
import express, { Request, Response } from 'express';
import { ofetch } from 'ofetch';

const router = express.Router();

const getPredictionData = async (req: Request, res: Response) => {
  try {
    const userId = req.query.user as string;

    const result = await userService.getPredictionData(userId);

    if (result.success) {
      res.json(result.data);
    } else {
      console.log('Prediction error: ' + result.error);
      res.status(422).json({ success: false, error: result.error });
    }
  } catch (error) {
    console.error('Prediction error:', error);
    res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

const healthCheck = async (req: Request, res: Response) => {
  try {
    const response = await ofetch('http://3.133.44.103:7777/healthcheck');
    const health = await response.json();

    if (health.status === 'running' || health.status === 'unhealthy') {
      res.json({ success: true });
    } else {
      res.status(500).json({ success: false, message: 'Healthcheck failed' });
    }
  } catch (error) {
    console.error('Prediction healthcheck error:', error);
    res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

/**
 * @swagger
 * /prediction:
 *   get:
 *     summary: Retrieve prediction data for a specific user
 *     description: Fetches prediction data based on a user's calendar entries and cycle history. Calculates average cycle length and provides the last period start date.
 *     tags:
 *       - Prediction
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *       - in: query
 *         name: user
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID for which prediction data is requested.
 *     responses:
 *       200:
 *         description: Prediction data successfully retrieved.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 average_cycle_length:
 *                   type: integer
 *                   example: 28
 *                 start_of_last_period:
 *                   type: string
 *                   format: date
 *                   example: "2024-10-01"
 *                 data:
 *                   type: array
 *                   description: Measurement data related to the prediction (if available).
 *                   items:
 *                     type: object
 *       401:
 *         description: Unauthorized access due to missing or invalid credentials.
 *       422:
 *         description: Unprocessable Entity - either due to lack of sufficient cycle data or a server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Not enough cycle data"
 */
router.get('/', getPredictionData);
/**
 * @swagger
 * /prediction/healthcheck:
 *   get:
 *     summary: Perform a health check on the prediction service
 *     description: Calls an external health check endpoint to confirm if the prediction service is operational.
 *     tags:
 *       - Prediction
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     responses:
 *       200:
 *         description: Health check performed successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *       500:
 *         description: Internal server error during health check.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Prediction healthcheck error"
 */
router.get('/healthcheck', healthCheck);

export default router;
