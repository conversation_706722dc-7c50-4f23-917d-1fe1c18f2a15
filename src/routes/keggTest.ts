import keggTestService, { AddKeggTestDataParams, GetKeggTestDataParams } from '@/services/kegg_test_service';
import dotenv from 'dotenv';
import express, { Request, Response } from 'express';

import User from '@/models/user';

dotenv.config();

const router = express.Router();
const writePwd = process.env.KEGG_TEST_WRITE_PWD;

const list = async (req: Request, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string;
    const user = await User.getUserFromToken(token);

    if (user.role != 'admin' && user.role != 'support' && user.role != 'readonly') {
      res.json({ success: false, message: 'Unauthorized' });
      return;
    }

    const params: GetKeggTestDataParams = {
      page: req.body.page,
      pagesize: req.body.pagesize,
      sortBy: req.body.sortBy,
      sort: req.body.sort,
    };

    res.json(await keggTestService.getKeggTestData(params));
    return;
  } catch (error) {
    console.log('keggTest list error: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const add = async (req: Request, res: Response) => {
  const { serial, success, result } = req.body;

  if (!serial || !result) {
    res.json({ success: false, message: 'Missing fields' });
    return;
  }

  try {
    const ip = req.ip;
    const params: AddKeggTestDataParams = { serial, success, result, ip };

    res.json(await keggTestService.addKeggTestData(params));
  } catch (error) {
    console.error('keggTest add error:', error);
    res.json({ success: false, error });
  }
};

const exp = async (req: Request, res: Response) => {
  try {
    res.setHeader('Content-Type', 'text/csv');

    const token = req.headers['x-access-token'] as string;
    const user = await User.getUserFromToken(token);

    if (user.role != 'admin' && user.role != 'support' && user.role != 'readonly') {
      res.json({ success: false, message: 'Unauthorized' });
      return;
    }

    res.send(await keggTestService.exportKeggTestData());
    return;
  } catch (error) {
    console.log('kegg tests export error: ' + error);
    res.json({ success: false, error: error });
    return;
  }
};

const getSerial = async (req: Request, res: Response) => {
  try {
    if (req.body.password !== writePwd) {
      res.json({ success: false, message: 'Unauthorized' });
      return;
    }

    res.json({ success: true, message: 'Ok', data: await keggTestService.createEmptyTest() });
    return;
  } catch (error) {
    console.log('keggTest getSerial error: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

/**
 * @swagger
 * /keggtest:
 *   post:
 *     summary: List Kegg tests with pagination and sorting
 *     description: Returns a paginated list of Kegg test records.
 *     tags:
 *       - KeggTest
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: integer
 *                 example: 0
 *               pagesize:
 *                 type: integer
 *                 example: 20
 *               sortBy:
 *                 type: string
 *                 example: "date"
 *               sort:
 *                 type: integer
 *                 example: -1
 *     responses:
 *       200:
 *         description: List of Kegg test records with pagination.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     pagesize:
 *                       type: integer
 *                     total:
 *                       type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       date:
 *                         type: string
 *                         format: date-time
 *                       success:
 *                         type: boolean
 *                       serial:
 *                         type: string
 *                       ip:
 *                         type: string
 *                       result:
 *                         type: string
 */
router.post('/', list);
/**
 * @swagger
 * /keggtest/add:
 *   post:
 *     summary: Add or update a Kegg test record
 *     description: Adds a new Kegg test or updates an existing one by serial.
 *     tags:
 *       - KeggTest
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               serial:
 *                 type: string
 *                 example: "AB123"
 *               success:
 *                 type: boolean
 *                 example: true
 *               result:
 *                 type: string
 *                 example: "Sample result"
 *     responses:
 *       200:
 *         description: Status of the add operation.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ok"
 */
router.post('/add', add);
/**
 * @swagger
 * /keggtest/export:
 *   post:
 *     summary: Export Kegg test data as CSV
 *     description: Exports Kegg test data in CSV format.
 *     tags:
 *       - KeggTest
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     responses:
 *       200:
 *         description: CSV data exported successfully.
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               example: "ts;success;serial;ip;data\n10/10/2024 12:00:00;true;AB123;127.0.0.1;Sample result"
 */
router.post('/export', exp);
/**
 * @swagger
 * /keggtest/getSerial:
 *   post:
 *     summary: Generate a new serial for Kegg testing
 *     description: Generates and returns a new serial for Kegg testing.
 *     tags:
 *       - KeggTest
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               password:
 *                 type: string
 *                 example: "your_write_password"
 *     responses:
 *       200:
 *         description: New serial generated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ok"
 *                 data:
 *                   type: object
 *                   properties:
 *                     _id:
 *                       type: string
 *                       example: "605c67e7e55b4e20a8f23456"
 */
router.post('/getSerial', getSerial);

export default router;
