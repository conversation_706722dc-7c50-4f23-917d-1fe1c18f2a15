import express, { Request, Response } from 'express';

import Measurement from '@/models/measurement';
import User from '@/models/user';
import '@/proto/kegel_info_pb';
import '@/proto/kegel_measurement_pb';
import measurementsService from '@/services/measurement_service';

const router = express.Router();

const findMeasurements = async (req: Request) => {
  const token = req.headers['x-access-token'] as string;
  const user = await User.getUserFromToken(token);

  if (!user || !['admin', 'support', 'readonly'].includes(user.role)) {
    return null;
  }

  const query: {
    _id?: string;
    user?: string;
    appVersion?: { $regex: string; $options: string };
    firmwareVersion?: { $regex: string; $options: string };
    date?: { $gte: number; $lte: number };
    lastMeasurement?: boolean;
    resultCode?: { $not: { $regex: string } };
  } = {};
  const limits: {
    skip?: number;
    limit?: number;
    sort?: { [key: string]: number };
  } = {};

  if (req.body.id) query._id = req.body.id;

  if (req.body.id != null) {
    query._id = req.body.id;
  } else {
    if (req.body.user != null) {
      query.user = req.body.user;
    }
    if (req.body.appVersion != null && req.body.appVersion.length > 0) {
      query.appVersion = { $regex: '.*' + req.body.appVersion + '.*', $options: 'i' };
    }
    if (req.body.firmware != null && req.body.firmware.length > 0) {
      query.firmwareVersion = { $regex: '.*' + req.body.firmware + '.*', $options: 'i' };
    }
    if (req.body.onlyErrors === true) {
      query.resultCode = { $not: { $regex: 'OK' } };
    }

    if (req.body.startDate && req.body.endDate) {
      query.date = {
        $gte: new Date(req.body.startDate).getTime(),
        $lte: new Date(req.body.endDate).getTime(),
      };
    }

    if ('lastMeasurement' in req.body) {
      query.lastMeasurement = req.body.lastMeasurement === true;
    } else {
      query.lastMeasurement = true;
    }

    if (req.body.includeBadData !== true) {
      //  query.resultCode = 'OK'
    }
  }

  const page = req.body.page || 0;
  const pagesize = parseInt(req.body.pagesize, 10) || 20;

  limits.skip = page * pagesize;
  limits.limit = pagesize;

  const sortByField = req.body.sortBy || 'date';
  const sortDirection = typeof req.body.sort === 'string' 
    ? parseInt(req.body.sort, 10) 
    : req.body.sort || -1; // Fallback to -1 if invalid

  limits.sort = {
    [sortByField]: sortDirection
  };

  const measurements = await Measurement.find(query, {}, limits);
  return { query, page, pagesize, measurements };
};

const list = async (req: Request, res: Response) => {
  try {
    const data = await findMeasurements(req);
    if (!data) {
      res.status(403).json({ success: false, message: 'Unauthorized' });
      return;
    }

    const { query, page, pagesize, measurements } = data;

    const result = await measurementsService.getMeasurements(query, page, pagesize, measurements);

    res.json({
      success: true,
      message: 'Ok',
      pagination: result.pagination,
      data: result.data,
    });
  } catch (error) {
    console.error('Measurement list error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};

const addMeasurement = async (req: Request, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string;
    if (!token) {
      res.status(401).json({ success: false, message: 'Missing access token' });
      console.log('Measurement add error: Missing access token');
      return;
    }

    const date = new Date(req.body.date);
    const user = await User.getUserFromToken(token);

    res.status(200).json(await measurementsService.addMeasurement(req.body, user, date));
  } catch (error) {
    console.log('Measurement add error: ' + error, req.body ?? {}, req.headers?.['x-access-token'] ?? 'No token provided');
    res.json({ success: false, message: error });
    return;
  }
};

const update = async (req: Request, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string;
    const user = await User.getUserFromToken(token);

    if (user.role != 'admin' && user.role != 'support') {
      res.json({ success: false, message: 'Unauthorized' });
      return;
    }

    res.json(await measurementsService.updateMeasurement(req.body.id, req.body.data));
    return;
  } catch (error) {
    console.log('Measurement update error: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const remove = async (req: Request, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string;
    const user = await User.getUserFromToken(token);

    if (user.role != 'admin') {
      res.json({ success: false, message: 'Unauthorized' });
      return;
    }

    res.json(await measurementsService.removeMeasurement(req.body.id));
    return;
  } catch (error) {
    console.log('Measurement remove error: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const exp = async (req: Request, res: Response) => {
  try {
    res.setHeader('Content-Type', 'text/csv');

    const token = req.headers['x-access-token'] as string;
    const user = await User.getUserFromToken(token);

    if (!['admin', 'support', 'readonly'].includes(user.role)) {
      res.json({ success: false, message: 'Unauthorized' });
      return;
    }

    const query: { user?: string; _id?: string } = {};
    if (req.body.user) query.user = req.body.user;
    if (req.body.id) query._id = req.body.id;

    const csvContent = await measurementsService.exportMeasurements(query);

    res.send(csvContent);
    return;
  } catch (error) {
    console.log('Measurement export error: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const exportAggregate = async (req: Request, res: Response) => {
  try {
    res.setHeader('Content-Type', 'text/csv');

    const data = await findMeasurements(req);
    if (!data) {
      return;
    }

    res.send(measurementsService.exportAggregate(data.measurements, req.body.values));
    return;
  } catch (error) {
    console.log('Measurement export aggregate error: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

/**
 * @swagger
 * /measurement:
 *   post:
 *     summary: List measurements with pagination
 *     description: Returns a paginated list of measurement records based on query parameters.
 *     tags:
 *       - Measurement
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: integer
 *                 example: 0
 *               pagesize:
 *                 type: integer
 *                 example: 20
 *               query:
 *                 type: object
 *                 additionalProperties: true
 *               sortBy:
 *                 type: string
 *                 example: "date"
 *               sort:
 *                 type: integer
 *                 example: -1
 *     responses:
 *       200:
 *         description: List of measurements with pagination.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ok"
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     pagesize:
 *                       type: integer
 *                     total:
 *                       type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                       keggId:
 *                         type: string
 *                       serverDate:
 *                         type: string
 *                         format: date-time
 *                       data:
 *                         type: array
 *                         items:
 *                           type: object
 *                       date:
 *                         type: string
 *                         format: date-time
 *                       protocolVersion:
 *                         type: string
 *                       measurementValue:
 *                         type: number
 *                       appVersion:
 *                         type: string
 *                       firmwareVersion:
 *                         type: string
 *                       phoneInfo:
 *                         type: object
 *                         properties:
 *                           platform:
 *                             type: string
 *                             example: "Android"
 *                           os:
 *                             type: string
 *                             example: "10.0"
 *                           brand:
 *                             type: string
 *                             example: "Samsung"
 *                           model:
 *                             type: string
 *                             example: "Galaxy S10"
 *                       user:
 *                         type: string
 *                         example: "user123"
 *                       keggInfos:
 *                         type: array
 *                         items:
 *                           type: object
 *                       sequenceInfo:
 *                         type: object
 *                       resultCode:
 *                         type: string
 *                         example: "success"
 *                       session:
 *                         type: array
 *                         items:
 *                           type: object
 *                       computedValues:
 *                         type: object
 *                       temperature:
 *                         type: number
 *                         example: 37.0
 *       400:
 *         description: Bad request due to missing or invalid parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Error: Invalid query parameters"
 */
router.post('/', list);
/**
 * @swagger
 * /measurement/add:
 *   post:
 *     summary: Add a new measurement
 *     description: Adds a new measurement record to the database and associates it with the user.
 *     tags:
 *       - Measurement
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - date
 *               - keggId
 *               - phoneInfo
 *               - sequenceInfo
 *               - resultCode
 *               - protocolVersion
 *               - appVersion
 *               - rawKeggInfos
 *               - data
 *               - session
 *             properties:
 *               date:
 *                 type: string
 *                 format: date-time
 *               keggId:
 *                 type: string
 *               phoneInfo:
 *                 type: object
 *                 properties:
 *                   platform:
 *                     type: string
 *                   os:
 *                     type: string
 *                   brand:
 *                     type: string
 *                   model:
 *                     type: string
 *               sequenceInfo:
 *                 type: object
 *               resultCode:
 *                 type: string
 *                 example: "OK"
 *               protocolVersion:
 *                 type: string
 *               appVersion:
 *                 type: string
 *               rawKeggInfos:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     date:
 *                       type: string
 *                       format: date-time
 *                     info:
 *                       type: string
 *               data:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: byte
 *               session:
 *                 type: object
 *     responses:
 *       200:
 *         description: Measurement successfully saved.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Measurement saved"
 *       400:
 *         description: Bad request due to missing or invalid parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid input data"
 *       500:
 *         description: Server error while processing the measurement.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Measurement add error"
 */
router.post('/add', addMeasurement);
/**
 * @swagger
 * /measurement/update:
 *   post:
 *     summary: Update a measurement
 *     description: Updates an existing measurement in the database.
 *     tags:
 *       - Measurement
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *             properties:
 *               id:
 *                 type: string
 *                 description: The ID of the measurement to update.
 *               data:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: byte
 *                 description: The new measurement data.
 *     responses:
 *       200:
 *         description: Measurement successfully updated.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ok"
 *       400:
 *         description: Bad request due to missing or invalid parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Measurement does not exist"
 *       401:
 *         description: Unauthorized access due to insufficient permissions.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Unauthorized"
 *       500:
 *         description: Server error while processing the measurement update.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Measurement update error"
 */
router.post('/update', update);
/**
 * @swagger
 * /measurement/remove:
 *   post:
 *     summary: Remove a measurement
 *     description: Removes an existing measurement from the database, along with associated calendar entries and user data.
 *     tags:
 *       - Measurement
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *             properties:
 *               id:
 *                 type: string
 *                 description: The ID of the measurement to remove.
 *     responses:
 *       200:
 *         description: Measurement successfully removed.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ok"
 *       400:
 *         description: Bad request due to missing or invalid parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Measurement does not exist"
 *       401:
 *         description: Unauthorized access due to insufficient permissions (only admin can remove measurements).
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Unauthorized"
 *       404:
 *         description: Calendar entry not found for the measurement.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Calendar does not exist"
 *       500:
 *         description: Server error while processing the measurement removal.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Measurement remove error"
 */
router.post('/remove', remove);
/**
 * @swagger
 * /measurement/export:
 *   post:
 *     summary: Export measurements to CSV
 *     description: Exports measurement data to a CSV file for a given user or measurement ID.
 *     tags:
 *       - Measurement
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user:
 *                 type: string
 *                 description: The ID of the user whose measurements to export (optional).
 *               id:
 *                 type: string
 *                 description: The ID of the measurement to export (optional).
 *     responses:
 *       200:
 *         description: CSV file with measurement data.
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               example: |
 *                 r,rp,rn,pm,nm id=12345
 *                 5.6,3.2,2.3,0.1,-0.1
 *                 7.8,4.2,3.1,0.2,-0.2
 *       400:
 *         description: Bad request due to invalid parameters or missing user/ID.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid request data"
 *       401:
 *         description: Unauthorized access due to insufficient permissions.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Unauthorized"
 *       500:
 *         description: Internal server error while generating or exporting the CSV file.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Measurement export error"
 */
router.post('/export', exp);
/**
 * @swagger
 * /measurement/export/aggregate:
 *   post:
 *     summary: Export aggregated measurement data to CSV
 *     description: Exports aggregated measurement data, including percentile values, for each measurement.
 *     tags:
 *       - Measurement
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               values:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                       description: The name of the measurement field (e.g., 'rp', 'rn').
 *                     percentile:
 *                       type: integer
 *                       description: The percentile value to calculate (e.g., 10, 30, 50, 70, 90).
 *                 description: An array of values specifying the name and percentile to calculate. If not provided, defaults to the specified set of values.
 *     responses:
 *       200:
 *         description: CSV file with aggregated measurement data.
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               example: |
 *                 rp10,rp30,rp50,rp70,rp90,rn10,rn30,rn50,rn70,rn90,id
 *                 1.2,3.4,5.6,7.8,9.0,0.1,0.3,0.5,0.7,0.9,12345
 *                 2.3,4.5,6.7,8.9,10.1,1.2,1.4,1.6,1.8,2.0,67890
 *       400:
 *         description: Bad request due to invalid parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid request data"
 *       401:
 *         description: Unauthorized access due to insufficient permissions.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Unauthorized"
 *       500:
 *         description: Internal server error while generating or exporting the aggregated CSV file.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Measurement export aggregate error"
 */
router.post('/export/aggregate', exportAggregate);

export default router;
