import User from '@/models/user';
import calendarService, { GetCycleProps, RemoveCycleProps } from '@/services/calendar_service';
import express, { Request, Response } from 'express';

const router = express.Router();

type ListBodyDTO = {
  id?: string;
  user?: string;
  startDate?: string;
  endDate?: string;
};

const list = async (req: Request<never, unknown, ListBodyDTO>, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string;
    const user = await User.getUserFromToken(token);
    const { id, user: userId, startDate, endDate } = req.body;
    const props: GetCycleProps = {};

    if (user.role == 'admin' || user.role == 'readonly') {
      if (id) {
        props.id = id;
      } else {
        if (userId) {
          props.userId = userId;
        }
        if (startDate && endDate) {
          props.dateRange = {
            $gte: new Date(startDate).getTime(),
            $lte: new Date(endDate).getTime(),
          };
        }
      }
    } else {
      props.userId = user.id;
    }

    const cycles = await calendarService.getCycles(props);
    res.json(cycles);
    return;
  } catch (error) {
    console.log('Cycle list error: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

type ListSuccessBodyDTO = {
  id?: string;
  user?: string;
  startDate?: string;
  endDate?: string;
  sort?: boolean;
};

const listWithSuccess = async (req: Request<never, unknown, ListSuccessBodyDTO>, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string;
    const user = await User.getUserFromToken(token);

    const query: GetCycleProps = {
      sort: req.body.sort,
    };

    if (user.role == 'admin' || user.role == 'readonly') {
      if (req.body.id != null) {
        query.id = req.body.id;
      } else {
        if (req.body.user != null) {
          query.userId = req.body.user;
        }
        if (
          req.body.startDate != null &&
          req.body.endDate != null &&
          req.body.startDate.length > 0 &&
          req.body.endDate.length > 0
        ) {
          const startTime = new Date(req.body.startDate);
          const endTime = new Date(req.body.endDate);
          query.dateRange = {
            $gte: startTime.getTime(),
            $lte: endTime.getTime(),
          };
        }
      }
    } else {
      query.userId = user.id;
    }

    const cycles = await calendarService.getCycles(query);

    res.json({ success: true, data: cycles });
    return;
  } catch (error) {
    console.log('Cycle list error: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

const removeCycle = async (req: Request, res: Response) => {
  try {
    const token = req.headers['x-access-token'] as string;
    const user = await User.getUserFromToken(token);

    if (user.role != 'admin') {
      res.json({ success: false, message: 'Unauthorized' });
      return;
    }

    const props: RemoveCycleProps = {
      userId: req.body.user,
      dateRange: {
        $gte: req.body.startDate,
        $lte: req.body.endDate,
      },
    };

    res.json(await calendarService.removeCycle(props));
    return;
  } catch (error) {
    console.log('Remove cycle error: ' + error);
    res.json({ success: false, message: error });
    return;
  }
};

/**
 * @swagger
 * /cycle/:
 *   post:
 *     summary: Retrieve a list of calendar cycles
 *     description: Returns a list of calendar cycles filtered by user role and specified parameters. Admin and readonly roles can filter by user ID, start date, and end date. Regular users can retrieve only their own cycles.
 *     tags:
 *       - Calendar
 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: ID of the cycle to retrieve (admin only).
 *                 example: "cycle12345"
 *               user:
 *                 type: string
 *                 description: The ID of the user whose cycles to retrieve (admin only).
 *                 example: "user12345"
 *               startDate:
 *                 type: string
 *                 format: date
 *                 description: Start date for filtering cycles (YYYY-MM-DD).
 *                 example: "2024-01-01"
 *               endDate:
 *                 type: string
 *                 format: date
 *                 description: End date for filtering cycles (YYYY-MM-DD).
 *                 example: "2024-12-31"
 *     responses:
 *       200:
 *         description: Success - List of filtered cycles
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   userId:
 *                     type: string
 *                   startDate:
 *                     type: string
 *                     format: date
 *                   endDate:
 *                     type: string
 *                     format: date
 *                   data:
 *                     type: object
 *       400:
 *         description: Error - Missing or invalid request data
 *       401:
 *         description: Error - Unauthorized access
 *       500:
 *         description: Error - Server error
 */
router.post('/', list);
/**
 * @swagger
 * /cycle/withSuccess:
 *   post:
 *     summary: Retrieve cycle data with period information and measurements
 *     description: Retrieves a list of cycles, each containing period days and measurement data. Admin and readonly users can filter by user ID, start date, and end date, while regular users can only retrieve their own data.
 *     tags:
 *       - Cycle


 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
*     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: ID of the specific cycle to retrieve (admin only).
 *                 example: "cycle12345"
 *               user:
 *                 type: string
 *                 description: User ID for cycle filtering (admin only).
 *                 example: "user12345"
 *               startDate:
 *                 type: string
 *                 format: date
 *                 description: Start date for filtering cycles (YYYY-MM-DD).
 *                 example: "2024-01-01"
 *               endDate:
 *                 type: string
 *                 format: date
 *                 description: End date for filtering cycles (YYYY-MM-DD).
 *                 example: "2024-12-31"
 *               sort:
 *                 type: boolean
 *                 description: Whether to sort the cycles in reverse order.
 *                 example: true
 *     responses:
 *       200:
 *         description: Success - List of cycles with period and measurement data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       startDate:
 *                         type: string
 *                         format: date
 *                         description: Start date of the cycle.
 *                       days:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             calendarId:
 *                               type: string
 *                               description: Calendar item ID.
 *                             day:
 *                               type: string
 *                               format: date
 *                               description: Day in the cycle.
 *                             val:
 *                               type: number
 *                               description: Last recorded value for the day.
 *                             lastMeasurementTime:
 *                               type: string
 *                               format: date-time
 *                               description: Timestamp of the last measurement.
 *                       length:
 *                         type: number
 *                         description: Duration of the cycle in days.
 *                       finished:
 *                         type: boolean
 *                         description: Whether the cycle is complete.
 *       400:
 *         description: Error - Missing or invalid request data
 *       401:
 *         description: Error - Unauthorized access
 *       500:
 *         description: Error - Server error
 */
router.post('/withSuccess', listWithSuccess);
/**
 * @swagger
 * /cycle/remove:
 *   post:
 *     summary: Remove period markings from calendar records within a date range
 *     description: Allows admin users to remove period data from calendar records for a specified user and date range.
 *     tags:
 *       - Cycle


 *     parameters:
 *       - $ref: '#/components/parameters/AccessToken'
*     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user:
 *                 type: string
 *                 description: ID of the user whose calendar records should be updated.
 *                 example: "user12345"
 *               startDate:
 *                 type: string
 *                 format: date
 *                 description: Start date for the date range (YYYY-MM-DD).
 *                 example: "2024-01-01"
 *               endDate:
 *                 type: string
 *                 format: date
 *                 description: End date for the date range (YYYY-MM-DD).
 *                 example: "2024-01-31"
 *     responses:
 *       200:
 *         description: Success - Period markings removed from calendar records
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                   example: "Ok"
 *       400:
 *         description: Error - Missing or invalid request data
 *       401:
 *         description: Error - Unauthorized access
 *       404:
 *         description: Error - Calendar records not found
 *       500:
 *         description: Error - Server error
 */
router.post('/remove', removeCycle);

export default router;
