import { Consts } from '@/consts';
import Calendar from '@/models/calendar';
import User from '@/models/user';
import { CalendarDay, CalendarDayData, Cycle, ForecastData, ICalendar, OvulationData, AndromedaPredictionsData } from '@/types/calendar';
import { IUser, SignUpData } from '@/types/user';
import { sendToMixpanel } from '@/utils/calendar';
import Measurement from '@/models/measurement';
import Mixpanel from 'mixpanel';
import moment, { Moment } from 'moment';
import { ofetch } from 'ofetch';
import { Types } from 'mongoose';

export type GetCycleProps = {
  id?: string;
  userId?: string;
  dateRange?: { $gte: number; $lte: number };
  sort?: boolean;
};

const getCycles = async ({ id, userId, dateRange, sort = true }: GetCycleProps): Promise<Cycle[]> => {
  const filter: Record<string, unknown> = {};
  if (id) filter._id = id;
  if (userId) filter.user = new Types.ObjectId(userId); // Convert to ObjectId
  if (dateRange) {
    filter.date = {
      $gte: new Date(dateRange.$gte), // Convert to Date
      $lte: new Date(dateRange.$lte),
    };
  }

  // Execute query
  const calendarItems = await Calendar.find(filter).sort({ date: 1 });

  const cycles: Cycle[] = [];
  let lastPeriodStart = null;
  let lastPeriodDay = null;

  for (let i = 0; i < calendarItems.length; i++) {
    const d = calendarItems[i];
    const day = moment.utc(d.date);
    const dayString = day.format('YYYY-MM-DD');
    const isPeriod = d.data != null && d.data.period != null && d.data.period;
    const isExcluded = d.data != null && d.data.excluded != null && d.data.excluded;
    const exclusionReason = d.data != null && d.data.exclusionReason ? d.data.exclusionReason : '';
    const isPeriodStart = isPeriod && (lastPeriodDay == null || day.diff(moment.utc(lastPeriodDay.date), 'days') != 1);

    if (isPeriodStart) {
      if (lastPeriodStart != null) {
        const lastPeriodStart = moment.utc(cycles[cycles.length - 1].startDate);
        cycles[cycles.length - 1].length = day.diff(lastPeriodStart, 'days');
        cycles[cycles.length - 1].finished = true;
      }

      cycles.push({
        startDate: dayString,
        days: [],
        excluded: isExcluded,
        exclusionReason: exclusionReason,
        finished: false,
      });

      lastPeriodStart = d;
    }

    if (cycles.length > 0 && d.values != null && d.values.length > 0) {
      cycles[cycles.length - 1].days.push({
        calendarId: d._id,
        day: dayString,
        val: d.values[d.values.length - 1],
        lastMeasurementTime: d.lastMeasurementTime,
      });
    }

    if (isPeriod) {
      lastPeriodDay = d;
      cycles[cycles.length - 1].periodLength = day.diff(moment.utc(cycles[cycles.length - 1].startDate), 'days') + 1;
    }
  }
  return sort ? cycles.reverse() : cycles;
};

export type RemoveCycleProps = {
  userId?: string;
  dateRange?: { $gte: Date; $lte: Date };
};

const removeCycle = async ({ userId, dateRange }: RemoveCycleProps): Promise<{ success: boolean; message: string }> => {
  const query = {
    user: userId,
    'data.period': true,
    date: dateRange,
  };

  const calendarsToEditExists = await Calendar.exists(query);

  if (!calendarsToEditExists) {
    return { success: false, message: 'Calendar records were not found' };
  }

  await Calendar.updateMany(query, { 'data.period': false });
  console.log('Remove cycle success');

  return { success: true, message: 'Ok' };
};

export type RemoveCalendarProps = {
  userId: string;
  date: Date;
};

const removeCalendar = async ({
  userId,
  date,
}: RemoveCalendarProps): Promise<{ success: boolean; message: string }> => {
  const calendarToRemove = await Calendar.findOne({
    userId,
    date,
  });
  if (calendarToRemove == null) {
    return { success: false, message: 'Calendar does not exist' };
  }

  await Calendar.deleteOne({ _id: calendarToRemove._id });
  await User.findOneAndUpdate({ _id: userId }, { $pull: { calendar: calendarToRemove._id } });

  return { success: true, message: 'Ok' };
};

export type UpdatePeriodsProps = {
  user: IUser;
  periods: {
    [key: string]: boolean;
  };
};

const countCycles = async (user: IUser) => {
  const calendar = await Calendar.find({ user: user._id }, {}, { sort: { date: 1 } });
  let cycleCount =
    calendar.length > 0 && calendar[calendar.length - 1].data && calendar[calendar.length - 1].data?.period === true
      ? 1
      : 0;

  for (let i = 0; i < calendar.length - 1; i++) {
    const c1 = calendar[i];
    const c2 = calendar[i + 1];
    const d1 = moment(c1.date);
    const d2 = moment(c2.date);
    const p1 = c1.data && c1.data.period === true;
    const p2 = c2.data && c2.data.period === true;

    if (p1 && !p2) {
      cycleCount++;
    } else if (p1 && p2) {
      if (d2.diff(d1, 'days') > 1) {
        cycleCount++;
      }
    }
  }

  return cycleCount;
};

const updatePeriods = async ({ user, periods }: UpdatePeriodsProps): Promise<{ success: boolean }> => {
  for (const key in periods) {
    const val = periods[key];
    const existing = await Calendar.findOne({ user: user._id, date: key });

    if (existing) {
      let data = existing.data;
      if (data == null) {
        data = {};
      }
      data.period = val;
      if(!val) data.periodBleeding = undefined;
      existing.data = null;
      existing.data = data;
      await existing.save();
    } else {
      const calendarItem = {
        user: user._id,
        date: key,
        data: { period: val === true },
      };

      const newItem = await Calendar.create(calendarItem);
      user.calendar.push(newItem);
    }
  }

  user.cycleCount = await countCycles(user);
  user.save();

  sendToMixpanel(user);

  return { success: true };
};

export type UpdateCalendarProps = {
  userId: string;
  date: Date;
  data: CalendarDayData | null;
};

const updateCalendar = async ({
  userId,
  date,
  data,
}: UpdateCalendarProps): Promise<{ success: boolean; message: string }> => {
  const calendarToUpdate = await Calendar.findOne({
    user: userId,
    date: date,
  });

  if (!calendarToUpdate) {
    return { success: false, message: 'Calendar does not exist' };
  }

  if (data) {
    calendarToUpdate.data = data;
    await calendarToUpdate.save();
  }

  return { success: true, message: 'Ok' };
};

const handleSignupData = async ({
  user,
  date,
  signupData,
  mixpanelApi,
}: {
  user: IUser;
  date: Date;
  signupData: Partial<SignUpData>;
  mixpanelApi?: Mixpanel.Mixpanel | null;
}): Promise<void> => {
  let nextDay = moment.utc(date);
  for (let i = 0; i < 5; i++) {
    //TODO: const period length
    const newItem = {
      user: user._id,
      date: nextDay,
      data: { period: true, valueAtLogin: true },
    };
    const newDay = await Calendar.create(newItem);
    user.calendar.push(newDay);
    nextDay = nextDay.add(1, 'day');
  }

  // Store user signup onboarding info
  user.info = {};
  if (signupData.keggIdentifier) {
    user.info.keggIdentifiers = [signupData.keggIdentifier];
  }

  delete signupData.keggIdentifier;
  user.info.profile = signupData;
  user.cycleCount = 1;
  await user.save();

  mixpanelApi?.track('Profile created', { distinct_id: user._id, time: new Date() });
  sendToMixpanel(user);
};

export type AddOrUpdateCalendarProps = {
  user: IUser;
  date: Date;
  type: string;
  data: CalendarDayData;
  signupData?: Partial<SignUpData>;
  mixpanelApi?: Mixpanel.Mixpanel | null;
};

const addOrUpdateCalendar = async ({
  user,
  date,
  type,
  data,
  signupData,
  mixpanelApi,
}: AddOrUpdateCalendarProps): Promise<{
  success: boolean;
  message: string;
  item?: { date: string; data: CalendarDayData | null; values: number[] } | null;
}> => {
  const calendarItem = {
    user: user._id,
    date: date,
    type: type,
    data: data,
  };

  if (signupData) {
    await handleSignupData({ user, date, signupData, mixpanelApi });
    return { success: true, message: 'Ok' };
  }

  // Update or Create calendar item
  let item = null;
  const existing = await Calendar.findOne({
    user: user._id,
    date,
  });

  if (existing == null) {
    item = await Calendar.create(calendarItem);
    user.calendar.push(item);
    await user.save();
  } else {
    if (existing.data != null && existing.data.period != null) {
      calendarItem.data.period = existing.data.period;
    }
    existing.data = calendarItem.data;
    item = await existing.save();
  }

  const ret = {
    date: moment(item.date).format('YYYY-MM-DD'),
    data: item.data,
    values: item.values,
  };

  return { success: true, message: 'Ok', item: ret };
};

export type GetCalendarDataParams = {
  userId: string;
  userRole: string;
  body: {
    id?: string;
    user?: string;
    startDate?: string;
    endDate?: string;
    pagesize?: string;
    page?: number;
    sort?: number;
  };
};

const createCalendarData = (calendarItem: ICalendar): CalendarDayData => {
  const ret: CalendarDayData = {};
  const data = calendarItem.data;

  if (data != null) {
    const keys: (keyof CalendarDayData)[] = [
      'lhTest',
      'progTest',
      'sex',
      'spotting',
      'temperature',
      'period',
      'pregnancy',
      'cervicalMucus',
      'sensation',
      'sexDrive',
      'infection',
      'notes',
      'excluded',
      'exclusionReason',
      'periodBleeding',
      'dailySupplements',
      'exercise',
    ] as const;

    keys.forEach((key) => {
      if (data[key] != null) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ret[key] = data[key] as any;
      }
    });
  }

  return ret;
};

const getCalendarData = async ({
  userId,
  userRole,
  body,
}: GetCalendarDataParams): Promise<{
  success: boolean;
  data?: {
    date: string;
    data: CalendarDayData | null;
    values: number[];
    lastMeasurementTime: string;
  }[];
  message?: string;
}> => {
  const query: {
    _id?: string;
    user?: string;
    date?: { $gte: number; $lte: number };
  } = {};
  const limits: {
    limit?: number;
    skip?: number;
    sort?: { date: number };
  } = {};

  if (['admin', 'support', 'readonly'].includes(userRole)) {
    if (body.id) {
      query._id = body.id;
    } else {
      if (body.user) {
        query.user = body.user;
      }
      if (body.startDate && body.endDate && body.startDate.length > 0 && body.endDate.length > 0) {
        const startTime = new Date(body.startDate);
        const endTime = new Date(body.endDate);
        query.date = {
          $gte: startTime.getTime(),
          $lte: endTime.getTime(),
        };
      }
    }
  } else {
    query.user = userId;
  }

  if (body.pagesize) {
    limits.limit = parseInt(body.pagesize);
  }

  if (body.page != null && limits.limit) {
    limits.skip = body.page * limits.limit;
  }

  limits.sort = {
    date: body.sort ? body.sort : -1,
  };

  const calendarItems = await Calendar.find(query, {}, limits);

  const ret = calendarItems.map((item) => ({
    date: moment.utc(item.date).format('YYYY-MM-DD'),
    data: createCalendarData(item),
    values: item.values,
    lastMeasurementTime: item.lastMeasurementTime,
  }));

  return { success: true, data: ret };
};

const combineCalendarData = (
  calendarItems: ICalendar[],
  data: Record<string, CalendarDay>,
): {
  success: boolean;
  lastPeriodStartDay: string | null;
  nextPeriodStartDay: string | null;
  data: CalendarDay[];
} => {
  const today = moment.utc();
  const assigned = new Set();

  // Merge calendar items
  calendarItems.forEach((calendarItem) => {
    const dayString = moment.utc(calendarItem.date).format('YYYY-MM-DD');
    const d: CalendarDay = {
      date: dayString,
      lastMeasurementTime: calendarItem.lastMeasurementTime,
      data: createCalendarData(calendarItem),
      values: calendarItem.values,
    };

    if (data[dayString]) {
      d.prediction = data[dayString].prediction;
      d.past = data[dayString].past;
    }

     // Add new entries from predictions and ensure each date is only added once
    if (!assigned.has(dayString)) {
      assigned.add(dayString);
      data[dayString] = d;
    }
  });

  // Convert & sort
  const arr = Object.values(data).sort((left, right) => moment.utc(left.date).diff(moment.utc(right.date)));

  // Calculate last & next period start days
  let lastPeriodStartDay: string | null = null;
  let nextPeriodStartDay: string | null = null;
  let dayBefore: CalendarDay | null = null;
  arr.forEach((d) => {
    const day = moment.utc(d.date);
    let isPeriodStart = false;

    if (
      dayBefore == null ||
      day.diff(moment.utc(dayBefore.date), 'days') != 1 ||
      dayBefore.data == null ||
      dayBefore.data.period == null ||
      dayBefore.data.period == false
    ) {
      isPeriodStart = d.data != null && d.data.period != null && d.data.period == true;
    }

    if (isPeriodStart) {
      //TODO: set this during iterating only the items from the database
      d.data = { ...d.data, periodStart: true };

      if (day.isSameOrBefore(today, 'days')) {
        lastPeriodStartDay = day.format('YYYY-MM-DD'); //TODO: get this while iterating only the items from database
      }
    }

    if (d.prediction != null && d.prediction.periodStart && nextPeriodStartDay == null) {
      nextPeriodStartDay = day.format('YYYY-MM-DD'); //todo take this from the prediction[0] element
    }

    dayBefore = d;
  });

  return {
    success: true,
    lastPeriodStartDay: lastPeriodStartDay,
    nextPeriodStartDay: nextPeriodStartDay,
    data: arr,
  };
};

const processPastOvulation = (data: Record<string, CalendarDay>, past: OvulationData) => {
  if (past.ovulation) {
    const ovulation = moment.utc(past.ovulation);
    const datesToUpdate = [
      { date: ovulation.clone().subtract(1, 'days'), key: 'beforeOvulation' },
      { date: ovulation.clone().subtract(2, 'days'), key: 'fertile' },
      { date: ovulation.clone().subtract(3, 'days'), key: 'fertile' },
      { date: ovulation.clone().add(1, 'days'), key: 'afterOvulation' },
    ];

    datesToUpdate.forEach(({ date, key }) => {
      const formattedDate = date.format('YYYY-MM-DD');
      if (!data[formattedDate]) {
        data[formattedDate] = { date: formattedDate, past: { [key]: true } };
      } else {
        data[formattedDate].past = { ...data[formattedDate].past, [key]: true };
      }
    });

    const ovulationDay = ovulation.format('YYYY-MM-DD');
    if (!data[ovulationDay]) {
      data[ovulationDay] = {
        date: ovulationDay,
        past: { method: past.method, ovulation: true },
      };
    } else {
      data[ovulationDay].past = { ...data[ovulationDay].past, ovulation: true };
    }
  }
};

const addPeriodPredictions = (
  data: Record<string, CalendarDay>,
  periodStart: Moment,
  periodEnd: Moment,
  today: Moment,
) => {
  if (periodStart.isBefore(periodEnd, 'days') && periodEnd.isAfter(today, 'day')) {
    let firstDay = true;
    for (let m = periodStart.clone(); m.isSameOrBefore(periodEnd); m.add(1, 'days')) {
      if (m.isSameOrAfter(today, 'days')) {
        //Take only future period predictions
        const day: CalendarDay = {
          date: m.format('YYYY-MM-DD'),
          prediction: { period: true },
        };

        if (firstDay) {
          day.prediction!.periodStart = true;
          firstDay = false;
        }

        data[day.date] = day;
      }
    }
  }
};

const addFertilePredictions = (
  data: Record<string, CalendarDay>,
  fertilityStart: Moment,
  fertilityEnd: Moment,
  ovulation: Moment,
) => {
  // //for (let m = fertilityStart.clone().subtract(4, 'days'); m.isSameOrBefore(fertilityEnd); m.add(1, 'days')) {
  for (let m = fertilityStart.clone(); m.isSameOrBefore(fertilityEnd); m.add(1, 'days')) {
    // if(!allFollowingFertile && m.isBefore(fertilityStart, 'days')) {
    //   let d1 = calDict[m.clone().subtract(2, 'days').format('YYYY-MM-DD')]
    //   let d2 = calDict[m.clone().subtract(1, 'days').format('YYYY-MM-DD')]

    //   if(d1 && d2 && d1 > d2) {
    //     allFollowingFertile = true
    //   } else {
    //     continue
    //   }
    // }
    const day: CalendarDay = {
      date: m.format('YYYY-MM-DD'),
      prediction: { fertile: true },
    };

    if (m.isSame(ovulation)) {
      day.prediction!.ovulation = true;
    } else if (m.diff(ovulation, 'days') === -1) {
      day.prediction!.beforeOvulation = true;
    } else if (m.diff(ovulation, 'days') === 1) {
      day.prediction!.afterOvulation = true;
    }

    data[day.date] = day;
  }
};

const processPredictions = (
  calendarItems: ICalendar[],
  predictions: ForecastData[],
  ovulationData: OvulationData[],
): Record<string, CalendarDay> => {
  const data: Record<string, CalendarDay> = {};
  const today = moment.utc();
  let shift: null | number = null;
  let lastPeriod: Moment | null = null;

  //compute when is the last period based purely on the calendar data
  calendarItems.forEach((item) => {
    if (item.date && item.data && item.data.period === true) {
      const date = moment.utc(item.date);
      if (!lastPeriod || date.isAfter(lastPeriod)) {
        lastPeriod = date;
      }
    }
  });

  
  predictions.forEach((prediction) => {
    const periodStart = moment.utc(prediction.period_start);
    const periodEnd = moment.utc(prediction.period_start).add(4, 'days'); //TODO: Replace hardcoded value
    const fertilityStart = moment.utc(prediction.fertility_window.start);
    const fertilityEnd = moment.utc(prediction.fertility_window.end);
    const ovulation = moment.utc(prediction.ovulation);
    
    //prediction is too far in the future
    if (periodStart.diff(today, 'days') > 90) {
      //TODO: const
      return;
    }
    const calDict: { [key: string]: number } = {};
    try {
      calendarItems.forEach((d) => {
        if (d && d.date && d.values && d.values.length > 0) {
          calDict[moment(d.date).format('YYYY-MM-DD')] = d.values[d.values.length - 1];
        }
      });
    } catch (error) {
      console.log(error);
    }

    if (shift == null && periodStart.isAfter(lastPeriod)) {
      if (today.isSameOrAfter(periodStart)) {
        shift = today.diff(periodStart, 'days') + 1;
      }
    }

    if (shift) {
      fertilityStart.add(shift, 'days');
      fertilityEnd.add(shift, 'days');
      ovulation.add(shift, 'days');
      periodStart.add(shift, 'days');
      periodEnd.add(shift, 'days');
    }

    //Fertile predictions
    if (fertilityStart.isBefore(fertilityEnd, 'days')) {
      addFertilePredictions(data, fertilityStart, fertilityEnd, ovulation);
    }

    //Period predictions
    addPeriodPredictions(data, periodStart, periodEnd, today);
  });

  ovulationData.forEach((past) => {
    processPastOvulation(data, past);
  });

  return data;
};

const predictionFetch = ofetch.create({
  baseURL: Consts.predictionUrl,
});

export type GetCalendarFeedParams = {
  _id?: string;
  user?: string;
};

const getCalendarFeed = async (
  query: GetCalendarFeedParams,
): Promise<{
  success: boolean;
  lastPeriodStartDay: string | null;
  nextPeriodStartDay: string | null;
  data: CalendarDay[];
}> => {
  //TODO: send only reasonable amount of data ( 1 year ?)
  const calendarItems = await Calendar.find(query, {}, {});

  if (!Consts.predictionUrl.length) {
    return combineCalendarData(calendarItems, {});
  }

  const dev = Consts.isDev ? 'true' : 'false';

  try {
    const bodyOvulation: OvulationData[] = await predictionFetch(
      `/confirm/ovulation?dev=${dev}&user=${query.user}&after=${moment().subtract(730, 'days').format('YYYY-MM-DD')}`,
    );

    const bodyForecast: ForecastData[] = await predictionFetch(`/prediction/forecast?dev=${dev}&user=${query.user}`);

    const pastData = bodyOvulation;
    const predictionsData = bodyForecast;
    const data = processPredictions(calendarItems, predictionsData, pastData);

    return combineCalendarData(calendarItems, data);
  } catch (parseError) {
    console.error('Error parsing prediction data:', parseError);
    return combineCalendarData(calendarItems, {});
  }
};

const prepareCycleRequest = async (userId: string, numCycles: number) => {
  const user = await User.findById(userId);
  const cycles = await getCycles({ userId: userId, sort: false });

  // Limit the number of cycles to the last 12
  cycles.splice(0, Math.max(0, cycles.length - 12));

  const preparedCycles = [];
  for (const [index, cycle] of cycles.entries()) {
    let cycleLength = cycle.length;
    let cycleFinished = true;

    if (!cycleLength) {
      // Use current "size" as last cycle length - this is what Andromeda knows currently.
      cycleLength = cycle.days.length;
      cycleFinished = false;
    }

    const cycleStart = moment(cycle.startDate, 'YYYY-MM-DD');
    const cycleEnd = cycleStart.clone().add(cycleLength - 1, 'days'); // Inclusive end date

    // Fetch measurements within cycle
    const measurements = await Measurement.find({
      user: userId,
      date: {
        $gte: cycleStart.toDate(),
        $lte: cycleEnd.toDate(),
      },
      resultCode: "OK"
    });

    // Group measurements by day and keep only the latest one
    const measurementsByDay = new Map();
    measurements.forEach(m => {
      const dayString = moment(m.date).format('YYYY-MM-DD');
      if (!measurementsByDay.has(dayString) || 
          moment(m.date).isAfter(moment(measurementsByDay.get(dayString).date))) {
        measurementsByDay.set(dayString, m);
      }
    });

    // Convert back to array
    const uniqueMeasurements = Array.from(measurementsByDay.values());

    preparedCycles.push({
      cycleId: index + 1,
      cycleStart: cycle.startDate,
      cycleLength: cycleLength,
      periodLength: cycle.periodLength || 5, // Default to 5 days
      measurements: uniqueMeasurements.map(m => ({
        date: moment(m.date).format('YYYY-MM-DD'),
        measurementValue: m.measurementValue,
      })),
    });
  }

  if (preparedCycles.length === 0) {
    // We need to form a request with at least one cycle
    preparedCycles.push({
      cycleId: 1,
      cycleStart: user?.info?.lastUnfinishedCycleStart || moment().format('YYYY-MM-DD'),
      cycleLength: user?.info?.profile?.initialCycleLength || 28,
      periodLength: user?.info?.profile?.initialPeriodLength || 5,
      measurements: [],
    });
  }

  return {
    typicalCycleLength: Number(user?.info?.profile?.initialCycleLength) || 28,
    typicalPeriodLength: Number(user?.info?.profile?.initialPeriodLength) || 5,
    numCycles: numCycles,
    fertileScoreThreshold: 0.5, // Threshold filtering when to consider fertile after normalization
    highFertileScoreThreshold: 0.7,
    cycles: preparedCycles,
    cyclePredictionCutoff: 12, // Maximum number of cycles to take into consideration for prediction
  };
};

const getCyclePredictionsAndromeda = async (
  userId: string,
  numCycles: number,
  testing: boolean = false,
): Promise<{
  success: boolean;
  data?: any;
  error?: string;
}> => {
  try {
    const request = await prepareCycleRequest(userId, numCycles);

    // Call andromeda service
    const url = testing ? Consts.predictionUrlAndromedaTesting : Consts.predictionUrlAndromeda;
    const response = await ofetch(url, {
      method: 'POST',
      body: request,
    });

    return { success: true, data: response };
  } catch (error: any) {
    console.error('Prediction error:', error, "User ID", userId);
    if (error.response?.status === 400) {
      return { success: false, error: error.data?.error || 'Invalid request' };
    }
    return { success: false, error: 'Failed to retrieve predictions' };
  }
};


/**
 * Extracts fertility information from Andromeda's model.
 * For each cycle prediction, we look at the fertility distribution.
 * Only dates with a non-zero probability are added.
 * a flag is set if the date falls within the fertile window.
 */
const extractAndromedaPredictions = (
  cycles: any[]
): Record<string, AndromedaPredictionsData> => {
  const predictions: Record<string, AndromedaPredictionsData> = {};

  let lastPredictedPeriodLength = 5;
  let lastCycleLength = 28;
  let lastCycleEnd: moment.Moment = moment.utc("1970-01-01", 'YYYY-MM-DD'); // Initialize with a default value

  cycles.forEach((cycle) => {
    // Parse the fertile window dates
    const windowStart = moment.utc(cycle.fertile_window.start, 'YYYY-MM-DD'); //Clinical FW
    const windowEnd = moment.utc(cycle.fertile_window.end, 'YYYY-MM-DD');
    const fertileStart = moment.utc(cycle.fertile_days.min, 'YYYY-MM-DD'); //Fertile Score above threshold 1 (currently above 50)
    const fertileEnd = moment.utc(cycle.fertile_days.max, 'YYYY-MM-DD');
    const highFertileStart = moment.utc(cycle.high_fertile_days.min, 'YYYY-MM-DD'); //Fertile Score above threshold 2 (currently above 70)
    const highFertileEnd = moment.utc(cycle.high_fertile_days.max, 'YYYY-MM-DD');
    const fertility_dist = cycle.fertility_dist;
    const ovulation_dist = cycle.ovulation_dist;
    const periodEnd = moment.utc(cycle.period_length.median, 'YYYY-MM-DD');
    const cycleEnd = moment.utc(cycle.cycle_length.median, 'YYYY-MM-DD');
    const ovDay = cycle.statistics.max_prob_ov_day ? moment.utc(cycle.statistics.max_prob_ov_day, 'YYYY-MM-DD') : null;

    // Iterate over each day - using fertility_dist as the source of indexing
    for (const dateStr in fertility_dist) {
      const fertility_prob = fertility_dist[dateStr];
      const ovulation_prob = ovulation_dist[dateStr];
      const dateMoment = moment.utc(dateStr, 'YYYY-MM-DD');
      const isFertile = dateMoment.isSameOrAfter(fertileStart) && dateMoment.isSameOrBefore(fertileEnd);
      const isHighFertile = dateMoment.isSameOrAfter(highFertileStart) && dateMoment.isSameOrBefore(highFertileEnd);
      const isFertileWindow = dateMoment.isSameOrAfter(windowStart) && dateMoment.isSameOrBefore(windowEnd);
      const isPeriod = dateMoment.isSameOrBefore(periodEnd);
      if(!predictions[dateStr]) {
        predictions[dateStr] = {
          fertilityProbability: fertility_prob,
          ovulationPropability: ovulation_prob,
          isFertile: isFertile || isFertileWindow,
          isFertileWindow: isHighFertile, //originally isFertileWindow, now replaced with isHighFertile for the sake of UX
          isPeriod: isPeriod,
          isTrueOvulation: false, //TODO not in andromeda yet
          isFutureCycle: false,
          isOVDay: ovDay ? dateMoment.isSame(ovDay) : false,
         };
      }

      if(dateMoment.isAfter(cycleEnd)) {
        break; //do not add predictions for days after the cycle end
      }
    }
    if(cycleEnd.isAfter(lastCycleEnd)) {
      //save the last period and cycle length for creating extra period days
      lastCycleEnd = cycleEnd;
      lastPredictedPeriodLength = 1 + moment.utc(cycle.period_length.median, 'YYYY-MM-DD').diff(moment.utc(Object.keys(fertility_dist)[0], 'YYYY-MM-DD'), 'days');
      lastCycleLength = 1 + moment.utc(cycle.cycle_length.median, 'YYYY-MM-DD').diff(moment.utc(Object.keys(fertility_dist)[0], 'YYYY-MM-DD'), 'days');
    }
  });

  // Add n extra predicted period days for the next 6 cycles:
  for (let futureCycle = 0; futureCycle <= 5; futureCycle++) {
    const cycleStart = lastCycleEnd.clone().add(1, 'days').add(futureCycle * lastCycleLength, 'days');
    for (let periodDay = 1; periodDay <= lastPredictedPeriodLength; periodDay++) {
      const dateStr = cycleStart.clone().add(periodDay, 'days').format('YYYY-MM-DD');
        predictions[dateStr] = {
          fertilityProbability: 0,
          ovulationPropability: 0,
          isFertile: false,
          isFertileWindow: false,
          isPeriod: true,
          isTrueOvulation: false,
          isFutureCycle: true,
          isOVDay: false,
        };
    } 
  }

  return predictions;
};

/**
 * Merges the calendar items with the new fertility predictions.
 * For every day in the calendar items the period (and other) data are preserved.
 * Additionally, any extra day coming only from predictions (i.e. not in the calendar)
 * will be added to the feed.
 *

 * Note: We derive period information from the calendar items. Predicted periods are in "prediction"
 * object.
 */
const combineCalendarDataV2 = (
  calendarItems: ICalendar[],
  andromedaPredictions: Record<string, AndromedaPredictionsData>
): {
  success: boolean;
  lastPeriodStartDay: string | null;
  nextPeriodStartDay: string | null;
  data: CalendarDay[];
} => {
  const today = moment.utc();
  const data: Record<string, CalendarDay> = {};

  // Merge calendar items into the dictionary, repack in known format.
  calendarItems.forEach((item) => {
    const dayString = moment.utc(item.date).format('YYYY-MM-DD');
    const dayObj: CalendarDay = {
      date: dayString,
      lastMeasurementTime: (!data[dayString] || !data[dayString].lastMeasurementTime) ? item.lastMeasurementTime : data[dayString].lastMeasurementTime,
      data: (!data[dayString] || !data[dayString].data) ? createCalendarData(item) : data[dayString].data,
      values: (!data[dayString] || !data[dayString].values || data[dayString].values.length === 0) ? item.values : data[dayString].values,
    };

    // If we have a fertility prediction for this day, merge it.
    if (andromedaPredictions[dayString]) {
      dayObj.prediction = {
        fertilityProbability: andromedaPredictions[dayString].fertilityProbability,
        ovulationProbability: andromedaPredictions[dayString].ovulationPropability,
        fertile: andromedaPredictions[dayString].isFertile,
        fertileWindow: andromedaPredictions[dayString].isFertileWindow,
        period: andromedaPredictions[dayString].isPeriod,
        ovulation: andromedaPredictions[dayString].isTrueOvulation,
        futureCycle: andromedaPredictions[dayString].isFutureCycle,
        ovDay: andromedaPredictions[dayString].isOVDay,
      };
    }
    data[dayString] = dayObj;
  });

  // Add extra days from fertility predictions that are not in the calendar.
  for (const dayString in andromedaPredictions) {
    if (!data[dayString]) {
      data[dayString] = {
        date: dayString,
        prediction: {
          fertilityProbability: andromedaPredictions[dayString].fertilityProbability,
          ovulationProbability: andromedaPredictions[dayString].ovulationPropability,
          fertile: andromedaPredictions[dayString].isFertile,
          fertileWindow: andromedaPredictions[dayString].isFertileWindow,
          period: andromedaPredictions[dayString].isPeriod,
          ovulation: andromedaPredictions[dayString].isTrueOvulation,
          futureCycle: andromedaPredictions[dayString].isFutureCycle,
        },
      };
    }
  }

  // Convert dictionary to array and sort by date.
  const arr = Object.values(data).sort((a, b) =>
    moment.utc(a.date).diff(moment.utc(b.date))
  );

  // Compute period start days based solely on calendar data.
  // (The new predictions engine does not yet provide period predictions.)
  let lastPeriodStartDay: string | null = null;
  let nextPeriodStartDay: string | null = null;
  let dayBefore: CalendarDay | null = null;

  arr.forEach((d) => {
    const dayMoment = moment.utc(d.date);
    let isPeriodStart = false;
    if (
      dayBefore == null ||
      dayMoment.diff(moment.utc(dayBefore.date), 'days') !== 1 ||
      !dayBefore.data ||
      dayBefore.data.period !== true
    ) {
      isPeriodStart = !!d.data && d.data.period === true;
    }
    if (isPeriodStart) {
      // Mark the day as a period start.
      d.data = { ...d.data, periodStart: true };
      if (dayMoment.isSameOrBefore(today, 'day')) {
        lastPeriodStartDay = dayMoment.format('YYYY-MM-DD');
      }
    }
    // Since new predictions do not include period start predictions, nextPeriodStartDay remains null.
    // TODO: Add period predictions here in the future.
    if (d.prediction && d.prediction.periodStart && !nextPeriodStartDay) {
      nextPeriodStartDay = dayMoment.format('YYYY-MM-DD');
    }
    dayBefore = d;
  });

  return {
    success: true,
    lastPeriodStartDay,
    nextPeriodStartDay,
    data: arr,
  };
};

/**
 * getCalendarFeedV2 builds a feed similar to the original getCalendarFeed
 * but using the new prediction engine.
 * It fetches calendar items and fertility predictions, and merges them.
 */
const getCalendarFeedV2 = async (
  query: GetCalendarFeedParams,
  testing: boolean = false
): Promise<{
  success: boolean;
  lastPeriodStartDay: string | null;
  nextPeriodStartDay: string | null;
  data: CalendarDay[];
}> => {
  // Fetch calendar items as before.
  // Disabled date constraint - uncomment to re-enable
  // const after = moment().subtract(6, 'months').toDate();
  const calendarItems = await Calendar.find(
    { ...query /*, date: { $gte: after }*/ },
    {},
    {}
  );

  // Prepare an empty predictions object.
  let predictions: Record<string, AndromedaPredictionsData> = {};

  // If a user id is provided, call the new prediction engine.
  if (query.user) {
    // Use a default number of cycles, for example 5.
    const cyclePredictionsRes = await getCyclePredictionsAndromeda(query.user, 7, testing);
    if (cyclePredictionsRes.success && cyclePredictionsRes.data) {
      predictions = extractAndromedaPredictions(cyclePredictionsRes.data);
    }
  }

  // Merge the calendar items with the new fertility predictions.
  const combined = combineCalendarDataV2(calendarItems, predictions);
  return combined;
};

const generateAdHocStatistics = async (): Promise<void> => {
  console.log('Starting ad-hoc statistics generation...');
  
  try {
    // Your statistics logic here
    // Example: Get all users and process their calendar data
    const users = await User.find({lastActivity: {
      $gte: new Date("2025-05-01T00:00:00.000Z")
    }}); 
    
    let csvContent = 'userId,email,andromeda,static1,static2,lh,cycleCount,darkGreenFWLength,lightGreenFWLength,keggSN,hasHCGPositive\n';
    //possible values: NA, PASS, FAIL

    let allUsersNum = users.length;
    console.log(`Found ${allUsersNum} users`);

    //Andromeda metric
    let usersWithConfirmedOVDay = 0;
    let usersWithOVDayPASS = 0;

    //Static metric
    let usersWithForecastedOVWithinStaticWindowSweep1 = 0;
    let usersWithForecastedOVWithinStaticWindowSweep2 = 0;

    //LH metric
    let usersWithPositiveLHTest = 0;
    let usersWithPositiveLHTestPASS = 0;

    //current user temps
    let currentUserAndromedaPass = 'NA';
    let currentUserStaticPass1 = 'NA';
    let currentUserStaticPass2 = 'NA';
    let currentUserLHTestPass = 'NA';
    let currentUserHasHCGPositive = false;


    for (const user of users) {
      currentUserAndromedaPass = 'NA';
      currentUserStaticPass1 = 'NA';
      currentUserStaticPass2 = 'NA';
      currentUserLHTestPass = 'NA';
      currentUserHasHCGPositive = false;

      const currentUserCycleCount = user.cycleCount;


      //get Calendar Data V2
      const calendarData = await getCalendarFeedV2({ user: user._id.toString() });
      if(!calendarData.success) {
        console.log(`Failed to get calendar data for user ${user._id}`);
        allUsersNum--;
        continue;
      }
      const calendarItems = calendarData.data;
      
      //First identify start and end date of the last full cycle
      let lastDayIndex = -1;
      let firstDayIndex = -1;
      //Find the end of previous cycle
      let lastDayWasPeriod = false;
      for (let i = calendarItems.length - 1; i >= 0; i--) {
        const d = calendarItems[i];
        
        if(d.data?.pregnancy) {
          currentUserHasHCGPositive = true;
        }

        if(d.data?.period === true) {
          lastDayWasPeriod = true;
        }
        else if(lastDayWasPeriod) {
          lastDayIndex = i;
          break;
        }
      }

      //Find start of previous cycle
      for (let i = lastDayIndex - 1; i >= 0; i--) {
        const d = calendarItems[i];
        
        if(d.data?.pregnancy) {
          currentUserHasHCGPositive = true;
        }

        if(d.data?.period === true) {
          firstDayIndex = i;
        }
        else if(firstDayIndex !== -1) {
          break;
        }
      }

      //Now we have the boundaries of the last finished cycle, let's compute metrics

      let fertileWindowLength = 0;
      let fertileLength = 0;

      //ANDROMEDA METRIC
      // Methodology: PASS is only if the confirmed OV day falls within the Dark (second grade) Fertile window or up to 2 days after. Everything else is FAIL.
      let distanceFromFertileWindow = -1;
      for(let i = firstDayIndex; i <= lastDayIndex; i++) {
        const d = calendarItems[i];
        //continue if prediction is not present
        if(!d || d.prediction === null) {
          continue;
        }

        //since we're here, also compute fertile window length and fertile length
        if(d.prediction?.fertileWindow) {
          fertileWindowLength++;
        }
        if(d.prediction?.fertile) {
          fertileLength++;
        }

        if(d.prediction?.fertileWindow) {
          distanceFromFertileWindow = 0;
        }
        else {
          distanceFromFertileWindow++;
        }

        if(d.prediction?.ovDay) {
          usersWithConfirmedOVDay++;
          currentUserAndromedaPass = 'FAIL';
          if(distanceFromFertileWindow <= 2 && distanceFromFertileWindow >= 0) {
            usersWithOVDayPASS++;
            currentUserAndromedaPass = 'PASS';
          }
          break;
        }
      }

      if(fertileWindowLength === 0) {
        //skip this user
        allUsersNum--;
        continue;
      }

      //STATIC METRIC
      //Sweep 1 Methodology: PASS is if the day with the highest OV probability falls within the 11-15 days before the end of the cycle
      //Sweep 2 Methodology: The same for 10-17 days before the end of the cycle
      let maxOvulationProbability = 0;
      let distanceFromEndOfCycle = 0;
      for(let i = firstDayIndex; i <= lastDayIndex; i++) {
        const d = calendarItems[i];
        //continue if prediction is not present
        if(!d || d.prediction === null) {
          continue;
        }
        if(d.prediction?.ovulationProbability && d.prediction?.ovulationProbability > maxOvulationProbability) {
          maxOvulationProbability = d.prediction?.ovulationProbability;
          distanceFromEndOfCycle = lastDayIndex - i;
        }
      }

      if(distanceFromEndOfCycle >= 11 && distanceFromEndOfCycle <= 15) {
        usersWithForecastedOVWithinStaticWindowSweep1++;
        currentUserStaticPass1 = 'PASS';
      }
      else {
        currentUserStaticPass1 = 'FAIL';
      }

      if(distanceFromEndOfCycle >= 10 && distanceFromEndOfCycle <= 17) {
        usersWithForecastedOVWithinStaticWindowSweep2++;
        currentUserStaticPass2 = 'PASS';
      }
      else {
        currentUserStaticPass2 = 'FAIL';
      }


      //LH METRIC
      //Caveat: Of course, we will filter out all users without a positive LH test within their last completed cycle
      //Methodology: PASS if at least one positive LH test falls within the Dark (second grade) Fertile window or up to 2 days after
      let currentUserHasPositiveLHTest = false;
      distanceFromFertileWindow = -1;
      for(let i = firstDayIndex; i <= lastDayIndex; i++) {
        const d = calendarItems[i];
        //continue if prediction is not present
        if(!d || d.prediction === null) {
          continue;
        }
        if(d.prediction?.fertileWindow) {
          distanceFromFertileWindow = 0;
        }
        else {
          distanceFromFertileWindow++;
        }

        if(d.data?.lhTest) {
          if(!currentUserHasPositiveLHTest) {
            usersWithPositiveLHTest++;
          }
          currentUserHasPositiveLHTest = true;
          currentUserLHTestPass = 'FAIL';
          if(distanceFromFertileWindow <= 2 && distanceFromFertileWindow >= 0) {
            usersWithPositiveLHTestPASS++;
            currentUserLHTestPass = 'PASS';
            break;
          }
        }
      }



      
      // Add to CSV
      csvContent += `${user._id},${user.email},${currentUserAndromedaPass},
      ${currentUserStaticPass1},${currentUserStaticPass2},${currentUserLHTestPass},
      ${currentUserCycleCount},${fertileWindowLength},${fertileLength},
      ${user.info?.keggIdentifiers?.[0] || 'NA'},${currentUserHasHCGPositive}\n`;
    }

    console.log(`Andromeda metric: ${usersWithConfirmedOVDay} users with confirmed OV day, of which ${usersWithOVDayPASS} have a PASS`);
    console.log(`Static Metric: ${allUsersNum} users with a forecasted OV day, of which ${usersWithForecastedOVWithinStaticWindowSweep1} have a PASS for sweep 1 and ${usersWithForecastedOVWithinStaticWindowSweep2} have a PASS for sweep 2`);
    console.log(`LH Metric: ${usersWithPositiveLHTest} users with at least one positive LH test, of which ${usersWithPositiveLHTestPASS} have a PASS`);

    // Save to file
    const fs = require('fs');
    const filePath = `./statistics_${moment().format('YYYY-MM-DD_HH-mm')}.csv`;
    fs.writeFileSync(filePath, csvContent);
    
    console.log(`Ad-hoc statistics saved to ${filePath}`);
  } catch (error) {
    console.error('Error generating ad-hoc statistics:', error);
  }
};

export default {
  getCycles,
  removeCycle,
  removeCalendar,
  updatePeriods,
  updateCalendar,
  addOrUpdateCalendar,
  getCalendarData,
  getCalendarFeed,
  getCyclePredictionsAndromeda,
  getCalendarFeedV2,
  prepareCycleRequest,
  generateAdHocStatistics,
};
