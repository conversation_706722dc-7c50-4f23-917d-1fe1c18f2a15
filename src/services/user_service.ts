import { Consts } from '@/consts';
import Calendar from '@/models/calendar';
import Measurement from '@/models/measurement';
import User from '@/models/user';
import { CalendarDayData, ForecastData, ICalendar, OvulationData } from '@/types/calendar';
import { IMeasurement } from '@/types/measurements';
import {
  Consent,
  DeviceInfo,
  IUser,
  RegularCycleType,
  StatisticsData,
  TrackingReason,
  TryingToConceiveDuration,
  UserInfo,
  UserDataV2 as UserDataV2,
  TrackingReasonV2,
  SignUpData,
  Medication,
  Condition,
} from '@/types/user';
import Emailer from '@/utils/emailer';
import bcrypt from 'bcrypt';
import crypto from 'crypto';
import Mixpanel from 'mixpanel';
import moment from 'moment';
import mongoose, { Types } from 'mongoose';
import { ofetch } from 'ofetch';
import verifyAppleToken from 'verify-apple-id-token';
import { OAuth2Client } from 'google-auth-library';

// Add new interfaces
interface SignUpV2Params {
  authorizationType: 'email' | 'apple' | 'google';
  identityToken: string;
  language: string;
}

interface LoginV2Params {
  authorizationType: 'email' | 'apple' | 'google';
  identityToken: string;
  language?: string;
}

const baseUserFilter = { role: 'user', test: { $ne: true }, enabled: { $eq: true } };
const ObjectId = mongoose.Types.ObjectId;

const calculateAgeGroups = (usersByAge: { dateOfBirth?: number }[]) => {
  const ageGroups = {
    users_18_24: 0,
    users_25_34: 0,
    users_35_44: 0,
    users_45_54: 0,
    users_55_64: 0,
    users_65: 0,
  };

  usersByAge.forEach((user) => {
    if (user.dateOfBirth) {
      const age = moment().diff(moment(user.dateOfBirth, 'MM/YYYY'), 'years');
      if (age >= 18 && age < 25) ageGroups.users_18_24++;
      else if (age >= 25 && age < 35) ageGroups.users_25_34++;
      else if (age >= 35 && age < 45) ageGroups.users_35_44++;
      else if (age >= 45 && age < 55) ageGroups.users_45_54++;
      else if (age >= 55 && age < 65) ageGroups.users_55_64++;
      else if (age >= 65) ageGroups.users_65++;
    }
  });
  return ageGroups;
};

const getStatistics = async (): Promise<{ success: boolean; data: StatisticsData }> => {
  const totalRegistered = await User.countDocuments(baseUserFilter);
  const iosUsers = await User.countDocuments({
    role: 'user',
    test: { $ne: true },
    enabled: { $eq: true },
    'info.deviceInfo.platform': 'ios',
  });
  const androidUsers = await User.countDocuments({
    role: 'user',
    test: { $ne: true },
    enabled: { $eq: true },
    'info.deviceInfo.platform': 'android',
  });
  const otherUsers = await User.countDocuments({
    role: 'user',
    test: { $ne: true },
    enabled: { $eq: true },
    $and: [{ 'info.deviceInfo.platform': { $ne: 'ios' } }, { 'info.deviceInfo.platform': { $ne: 'android' } }],
  });

  const totalWithMoreThanOneMeasurement = await User.aggregate()
    .match(baseUserFilter)
    .project({ measurementsCount: { $size: { $ifNull: ['$measurements', []] } } })
    .match({ measurementsCount: { $gt: 1 } })
    .count('count');

  const usersByAge = await User.aggregate().match(baseUserFilter).project({ dateOfBirth: '$info.profile.dateOfBirth' });
  const ageGroups = calculateAgeGroups(usersByAge);

  const totalWithMoreThanTwoExercisesCount = await Measurement.aggregate([
    { $match: { 'sequenceInfo.exercise': true } },
    { $group: { _id: '$user', exerciseCount: { $sum: 1 } } },
    { $match: { exerciseCount: { $gte: 2 } } },
    { $count: 'users' },
  ]);

  const usersByPurpose = await User.aggregate([
    { $match: baseUserFilter },
    { $group: { _id: '$info.profile.trackingReason', count: { $sum: 1 } } },
  ]);

  const purposeData = usersByPurpose.reduce((acc, { _id, count }) => {
    acc[_id] = count;
    return acc;
  }, {});

  const usersByPurposeData = {
    trying_to_conceive: purposeData.trying_to_conceive || 0,
    cycle_info_tracking_fertility: purposeData.cycle_info_tracking_fertility || 0,
    not_sure_yet: purposeData.not_sure_yet || 0,
  };

  const data: StatisticsData = {
    totalRegistered: totalRegistered,
    usersByPlatform: { ios: iosUsers, android: androidUsers, others: otherUsers },
    usersByAge: ageGroups,
    usersByPurpose: usersByPurposeData,
    totalWithMoreThanOneMeasurement: totalWithMoreThanOneMeasurement[0]?.count || 0,
    totalWithMoreThanTwoExercises: totalWithMoreThanTwoExercisesCount[0]?.users || 0,
  };
  return { success: true, data: data };
};

const getPredictionData = async (
  userId: string,
): Promise<{
  success: boolean;
  data?: {
    average_cycle_length: number;
    start_of_last_period: string | null;
    data: never[];
  };
  error?: string;
}> => {
  if (!userId) {
    return { success: false, error: 'Missing data' };
  }

  const existingUser = await User.findOne({ _id: new Types.ObjectId(userId) });
  if (!existingUser) {
    return { success: false, error: 'User not found' };
  }

  const calendarEntries = await Calendar.find({ user: existingUser._id }).sort({ date: 1 });

  if (calendarEntries.length === 0) {
    return { success: false, error: 'Not enough cycle data' };
  }

  const today = moment.utc();
  let lastCycleStartBeforeToday = null;
  let lastCycleStart = null;
  let dayBefore = null;
  const cycleLengths = [];

  for (const d of calendarEntries) {
    const day = moment.utc(d.date);
    let isCycleStart = false;

    if (dayBefore == null || day.diff(moment.utc(dayBefore.date), 'days') !== 1 || !dayBefore.data?.period) {
      isCycleStart = d.data?.period === true;
    }

    if (isCycleStart) {
      if (lastCycleStart != null) {
        cycleLengths.push(day.diff(lastCycleStart, 'days'));
      }
      lastCycleStart = day;
      if (lastCycleStart.isBefore(today, 'days')) {
        lastCycleStartBeforeToday = lastCycleStart;
      }
    }

    dayBefore = d;
  }

  if (
    cycleLengths.length === 1 &&
    existingUser.info?.profile?.initialCycleLength !== undefined &&
    existingUser.info.profile.initialCycleLength !== 'not_sure'
  ) {
    cycleLengths.push(parseInt(existingUser.info.profile.initialCycleLength));
  }

  const avg = cycleLengths.length > 0 ? cycleLengths.reduce((a, b) => a + b) / cycleLengths.length : 0;
  let result;

  if (avg >= 21) {
    // TODO: const
    result = {
      average_cycle_length: avg,
      start_of_last_period: lastCycleStartBeforeToday ? lastCycleStartBeforeToday.format('YYYY-MM-DD') : null,
      data: [], //TODO: send also measurement data
    };
  } else {
    const average_cycle_length =
      existingUser.info?.profile?.initialCycleLength === undefined ||
      existingUser.info.profile.initialCycleLength === 'not_sure'
        ? 28
        : parseInt(existingUser.info.profile.initialCycleLength);

    result = {
      average_cycle_length,
      start_of_last_period: lastCycleStartBeforeToday ? lastCycleStartBeforeToday.format('YYYY-MM-DD') : null,
      data: [],
    };

    if (result.average_cycle_length == null) {
      result.average_cycle_length = existingUser.info?.lastFinishedCycleLength
        ? parseInt(existingUser.info.lastFinishedCycleLength)
        : 0;
    }
  }

  return { success: true, data: result };
};

interface GetUsersParams {
  user: IUser;
  body: {
    id?: string;
    email?: string;
    name?: string;
    trackingReason?: string;
    pregnancy?: boolean;
    appVersion?: string;
    firmware?: string;
    serial?: string;
    page?: number;
    pagesize?: string;
    sortBy?: string;
    sort?: number;
  };
}

type UserQuery = {
  _id?: string;
  email?: { $regex: string; $options: string };
  group?: string;
  $or?: { [key: string]: { $regex: string; $options: string } }[];
  'info.profile.firstName'?: { $regex: string; $options: string };
  'info.profile.lastName'?: { $regex: string; $options: string };
  'info.profile.trackingReason'?: string;
  'calendar.data.pregnancy'?: boolean;
  'info.appVersion'?: { $regex: string; $options: string };
  'info.lastKeggFirmwareVersion'?: { $regex: string; $options: string };
  'info.keggIdentifiers'?: { $regex: string; $options: string };
};

const getUsers = async ({ user, body }: GetUsersParams) => {
  const query: UserQuery = {};

  if (['admin', 'support', 'readonly'].includes(user.role)) {
    if (body.id) {
      query._id = body.id;
    } else {
      if (body.email) {
        query.email = { $regex: `.*${body.email}.*`, $options: 'i' };
      }
      if (user.group) {
        query.group = user.group;
      }
      if (body.name) {
        query.$or = [
          { 'info.profile.firstName': { $regex: `.*${body.name}.*`, $options: 'i' } },
          { 'info.profile.lastName': { $regex: `.*${body.name}.*`, $options: 'i' } },
        ];
      }
      if (body.trackingReason) {
        query['info.profile.trackingReason'] = body.trackingReason;
      }
      if (body.pregnancy) {
        query['calendar.data.pregnancy'] = true;
      }
      if (body.appVersion) {
        query['info.appVersion'] = { $regex: `.*${body.appVersion}.*`, $options: 'i' };
      }
      if (body.firmware) {
        query['info.lastKeggFirmwareVersion'] = { $regex: `.*${body.firmware}.*`, $options: 'i' };
      }
      if (body.serial) {
        query['info.keggIdentifiers'] = { $regex: `.*${body.serial}.*`, $options: 'i' };
      }
    }
  } else {
    query._id = user._id.toString();
  }

  const page = body.page || 0;
  const pagesize = body.pagesize ? parseInt(body.pagesize) : 20;

  const users = query._id
    ? await User.find(query).lean()
    : await User.aggregate([
        { $match: query },
        { $sort: { [body.sortBy || 'created']: body.sort === 1 || body.sort === -1 ? body.sort : -1 } },
        { $skip: page * pagesize },
        { $limit: pagesize },
      ]);

  const formattedUsers = users.map((user) => ({
    _id: user._id,
    email: user.email,
    phone: user.phone,
    group: user.group,
    measurementsCount: user.measurements.length,
    calendarCount: user.calendar.length,
    enabled: user.enabled,
    test: user.test,
    created: user.created,
    lastActivity: user.lastActivity,
    role: user.role,
    info: user.info,
    cycleCount: user.cycleCount,
  }));

  return formattedUsers;
};

const klaviyoSendEvent = async (
  email: string,
  eventAction: string,
  eventName: string,
  userProperties: Record<string, string> = {}
) => {
  try {
    const url = 'https://a.klaviyo.com/api/events';
    const options = {
      method: 'POST',
      headers: {
      accept: 'application/vnd.api+json',
      revision: '2025-01-15',
      'content-type': 'application/vnd.api+json',
      Authorization: `Klaviyo-API-Key ${Consts.klaviyoAPIKey}`,
      },
      body: JSON.stringify({
      data: {
        type: 'event',
        attributes: {
        properties: {
          action: eventAction, // event action ID
        },
        metric: {
          data: {
          type: 'metric',
          attributes: {
            name: eventName, // user-friendly event name
            service: 'api',
          },
          },
        },
        profile: {
          data: {
          type: 'profile',
          attributes: {
            email: email, // user email
            ...(userProperties && { properties: userProperties }), // include properties only if userProperties is defined
          },
          },
        },
        },
        unique_id: crypto.randomUUID().toString(),
      },
      }),
    };

    fetch(url, options).catch((err) => console.error(err));
  } catch (err) {
    console.error('Send Event to Klaviyo error:', err);
  }
};

const klaviyoSendProfile = async (email: string) => {
  try {
    const url = 'https://a.klaviyo.com/api/profile-bulk-import-jobs/';
    const apiKey = Consts.klaviyoAPIKey;

    const payload = {
      data: {
      type: 'profile-bulk-import-job',
      attributes: {
        profiles: {
        data: [
          {
          type: 'profile',
          attributes: {
            email: email,
          },
          },
        ],
        },
      },
      relationships: {
        lists: {
        data: [
          {
          type: 'list',
          id: 'QWBnta', //ID for appUsers list
          },
        ],
        },
      },
      },
    };

    const response = await ofetch(url, {
      method: 'POST',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: `Klaviyo-API-Key ${apiKey}`,
        revision: '2024-07-15',
      },
      body: payload,
    });
  } catch (err) {
    console.error('Send to Klaviyo error:', err);
  }
};

const signUp = async (body: {
  email: string;
  password: string;
  language: string;
  needToVerifyEmail: boolean;
}): Promise<{
  success: boolean;
  token?: string;
  message: string;
  user?: {
    _id: string;
    email: string;
    info?: UserInfo | null;
  };
  error?: string;
  errorCode?: number;
}> => {
  const hash = await bcrypt.hash(body.password, 10);

  const userData = {
    email: body.email,
    password: hash,
    role: 'user',
    language: body.language,
    emailVerified: body.needToVerifyEmail === true ? false : null,
  };

  const user = await User.findOne({ email: { $regex: new RegExp(userData.email, 'i') } });
  if (user) {
    console.log('User create error: Email already exists');
    return { success: false, message: 'Email already exists', errorCode: 1 };
  }

  const newUser = await User.create(userData);
  const token = await User.makeToken(newUser._id.toString());

  const ret = {
    _id: newUser._id.toString(),
    email: newUser.email,
    info: newUser.info,
  };

  klaviyoSendProfile(body.email);
  console.log('User signed up: ' + newUser._id);

  return { success: true, token, message: 'Signup successful', user: ret };
};

const sendTokenBySMS = async (number: string, token: string) => {
  try {
    const url = 'https://portal.bulkgate.com/api/1.0/simple/transactional';
    const body = {
      application_id: process.env.APP_ID,
      application_token: process.env.APP_TOKEN,
      number,
      text: `Your kegg login token is: ${token}`,
      sender_id: 'kegg',
      sender_id_value: 'kegg',
    };

    const response = await ofetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    console.log('SMS sent successfully:', response);
    return response;
  } catch (error) {
    console.error('Error sending SMS:', error);
    throw new Error('Failed to send SMS');
  }
};

const login = async (
  email: string,
  password: string,
  token: string,
): Promise<{
  success: boolean;
  token?: string;
  message: string;
  user?: {
    _id: string;
    email: string;
    info?: UserInfo | null;
  };
}> => {
  const user = await User.authenticate(email, password);

  if (['admin', 'support', 'readonly'].includes(user.role)) {
    if (user.phone && user.phone.length > 0) {
      // Generate and send a new token if necessary
      if (!user.token) {
        const newToken = Math.floor(100000 + Math.random() * 900000).toString();
        user.token = newToken;
        await user.save();
        await sendTokenBySMS(user.phone, newToken);
        return { success: false, message: 'New token sent' };
      }

      // Validate the provided token
      if (user.token !== token) {
        return { success: false, message: 'Invalid token' };
      }
    }
  }

  // Handle user login
  if (user.enabled) {
    const authToken = await User.makeToken(user._id.toString());

    const userInfo = {
      _id: user._id.toString(),
      email: user.email,
      info: user.info,
    };

    // Clear the token after successful login
    user.token = null;
    await user.save();

    console.log('User logged in:', user._id);
    return {
      success: true,
      token: authToken,
      message: 'Authentication successful',
      user: userInfo,
    };
  } else {
    console.error('User login error: Account disabled.', user._id);
    return { success: false, message: 'Account disabled' };
  }
};

const verifyUser = async (
  token: string,
  language: string,
  appVersion: string,
  deviceInfo: DeviceInfo,
  lastKeggFirmwareVersion: string,
): Promise<{
  success: boolean;
  message: string;
  info?: UserInfo;
  userId?: string;
  onboard?: boolean;
  created?: string;
}> => {
  const user = await User.getUserFromToken(token);
  user.lastActivity = new Date();

  const info = user.info || {};

  if (language) info.language = language;
  if (appVersion) info.appVersion = appVersion;
  if (deviceInfo) info.deviceInfo = deviceInfo;
  if (lastKeggFirmwareVersion) info.lastKeggFirmwareVersion = lastKeggFirmwareVersion;

  user.info = info;
  user.markModified('info');
  await user.save();

  if (!user.enabled) {
    return { success: false, message: 'Account disabled' };
  }

  return {
    success: true,
    message: 'Ok',
    info: info,
    userId: user._id.toString(),
    onboard: user.info.profile != null,
    created: user.created.toISOString(),
  };
};

export type CreateUserParams = {
  token: string;
  data: {
    email: string;
    password: string;
    role: string;
    enabled: boolean;
    test: boolean;
    phone: string;
    group: string;
    firstName: string;
    lastName: string;
    note: string;
    keggIdentifiers: string;
    lastUnfinishedCycleStart: string;
    lastFinishedCycleLength: string;
    initialCycleLength: string;
  };
};

const validateRequiredFields = (data: CreateUserParams['data']) => {
  const missing: string[] = [];
  if (!data.email) missing.push('email');
  if (!data.password) missing.push('password');
  if (!data.role || !['admin', 'support', 'readonly', 'user'].includes(data.role)) missing.push('role');
  return missing;
};

const createUserInfo = (data: CreateUserParams['data']): IUser['info'] => {
  const info: IUser['info'] = {
    profile: {
      firstName: data.firstName,
      lastName: data.lastName,
    },
    note: data.note,
    keggIdentifiers: data.keggIdentifiers ? [data.keggIdentifiers] : [],
  };

  if (data.lastUnfinishedCycleStart) info.lastUnfinishedCycleStart = data.lastUnfinishedCycleStart;
  if (data.lastFinishedCycleLength) info.lastFinishedCycleLength = data.lastFinishedCycleLength;

  if (
    data.initialCycleLength != null &&
    (typeof data.initialCycleLength === 'number' || data.initialCycleLength === 'not_sure')
  ) {
    if (!info.profile) {
      info.profile = {};
    }
    info.profile.initialCycleLength = data.initialCycleLength;
  }

  return info;
};

const createUser = async ({
  token,
  data,
}: CreateUserParams): Promise<{
  success: boolean;
  message: string;
  data?: IUser;
}> => {
  await User.getUserFromToken(token);

  const newUser: Partial<IUser> = {
    lastActivity: new Date(),
  };
  const missing: string[] = validateRequiredFields(data);

  if (data.email) {
    newUser.email = data.email;
  }

  if (data.password) {
    newUser.password = await bcrypt.hash(data.password, 10);
  }

  if (['admin', 'support', 'readonly', 'user'].includes(data.role)) {
    newUser.role = data.role;
  }

  newUser.enabled = data.enabled;
  newUser.test = data.test;
  newUser.phone = data.phone;
  newUser.group = data.group;

  const info: IUser['info'] = createUserInfo(data);

  newUser.info = info;

  if (missing.length > 0) {
    return { success: false, message: `Missing fields: <br>${missing.join(', ')}` };
  }

  const createdUser = await User.create(newUser);

  console.log('User created: ' + createdUser._id);
  return { success: true, message: 'Ok', data: createdUser };
};

const sendToMixpanel = (user: IUser, mixpanelApi: Mixpanel.Mixpanel | null) => {
  if (Consts.environment == 'live' && mixpanelApi && user.info != null && user.info.profile != null) {
    mixpanelApi.people.set(user._id.toString(), {
      $created: user.created,
      cycle: user.info.profile.regularCycle ?? '',
      initialCycleLength: user.info.profile.initialCycleLength ?? '',
      trackingReason: user.info.profile.trackingReason ?? '',
    });
  }
};

export type UpdateUserData = {
  id: string;
  email?: string;
  phone: string;
  group: string; //still should be available for admins
  enabled: boolean; //still should be available for admins
  test: boolean; //still should be available for admins
  note: string; //still should be available for admins
  lastUnfinishedCycleStart: string;
  lastFinishedCycleLength: string;
  initialCycleLength: string;
  keggIdentifiers: string;
  trackingReason?: TrackingReason;
  regularCycle?: RegularCycleType | null;
  tryingToConceiveDuration: TryingToConceiveDuration | null;
  height: string;
  weight: string;
  supplements: string;
  role: string;
  password: string;
  consent: Consent;
  firstName: string;
  lastName: string;
};

const updateUser = async (
  token: string,
  data: UpdateUserData,
  mixpanelApi: Mixpanel.Mixpanel | null,
): Promise<{
  success: boolean;
  message: string;
  profile?: UserInfo['profile'];
}> => {
  const user = await User.getUserFromToken(token);

  let id = data.id;
  if (user.role !== 'admin' && user.role !== 'support') {
    id = user._id.toString();
  }

  const userToUpdate = await User.findOne({ _id: id });

  if (!userToUpdate) {
    return { success: false, message: 'User does not exist' };
  }

  // Update basic user fields
  if (data.email) userToUpdate.email = data.email;
  if (data.phone) userToUpdate.phone = data.phone;
  if (data.group) userToUpdate.group = data.group;
  if (data.enabled != null) userToUpdate.enabled = data.enabled;
  if (data.test != null) userToUpdate.test = data.test;

  // Handle user info updates
  const info = userToUpdate.info ? userToUpdate.info : {};
  info.note = data.note;
  if (data.lastUnfinishedCycleStart) info.lastUnfinishedCycleStart = data.lastUnfinishedCycleStart;
  if (data.lastFinishedCycleLength) info.lastFinishedCycleLength = data.lastFinishedCycleLength;

  // Handle profile updates
  if (!info.profile) info.profile = {};
  if (data.initialCycleLength != null && (parseInt(data.initialCycleLength) || data.initialCycleLength == 'not_sure')) {
    info.profile.initialCycleLength = data.initialCycleLength;
  }
  if (data.keggIdentifiers) info.keggIdentifiers = [data.keggIdentifiers];

  if (['', 'trying_to_conceive', 'cycle_info_tracking_fertility', 'not_sure_yet'].includes(data.trackingReason || '')) {
    info.profile.trackingReason = data.trackingReason || null;
  }

  if (data.regularCycle) {
    if (data.regularCycle === 'not_specified') {
      info.profile.regularCycle = null;
    } else {
      info.profile.regularCycle = data.regularCycle;
    }
  }

  if (data.tryingToConceiveDuration) info.profile.tryingToConceiveDuration = data.tryingToConceiveDuration;
  if (data.height) info.profile.height = data.height;
  if (data.weight) info.profile.weight = data.weight;

  if ('supplements' in data) {
    if (info.profile.supplements) {
      info.profile.supplementsHistory = info.profile.supplementsHistory || {};
      info.profile.supplementsHistory[moment().format('YYYY-MM-DD HH:mm:ss')] = info.profile.supplements;
    }
    info.profile.supplements = data.supplements;
  }

  if (data.role && ['admin', 'support', 'readonly', 'user'].includes(data.role)) {
    userToUpdate.role = data.role;
  }

  if (data.password) {
    userToUpdate.password = await bcrypt.hash(data.password, 10);
  }

  info.profile.consent = info.profile.consent || {} as Consent;
  if (data.consent?.research != null) {
    info.profile.consent.research = data.consent.research;
  }
  if (data.consent?.keggPlus != null) {
    info.profile.consent.keggPlus = data.consent.keggPlus;
  }

  if (data.firstName) info.profile.firstName = data.firstName;
  if (data.lastName) info.profile.lastName = data.lastName;

  userToUpdate.info = info;
  userToUpdate.markModified('info');
  await userToUpdate.save();

  const profile = userToUpdate.info ? userToUpdate.info.profile : {};

  sendToMixpanel(userToUpdate, mixpanelApi);

  console.log('User updated: ' + userToUpdate._id);
  return { success: true, message: 'Ok', profile: profile };
};

const changePassword = async (
  token: string,
  password: string,
): Promise<{
  success: boolean;
  message: string;
}> => {
  if (!password) {
    return { success: false, message: 'Missing fields' };
  }

  const user = await User.getUserFromToken(token);
  if (!user) {
    return { success: false, message: 'User not found' };
  }
  user.password = await bcrypt.hash(password, 10);
  user.save();

  console.log('Password changed: ' + user._id);
  return { success: true, message: 'Changed' };
};

const removeUser = async (
  token: string,
  userId: string,
): Promise<{
  success: boolean;
  message: string;
}> => {
  const user = await User.getUserFromToken(token);
  if (!user) {
    return { success: false, message: 'User not found' };
  }
  const id = user.role == 'admin' ? userId : user._id;

  await Measurement.deleteMany({ user: id });
  await Calendar.deleteMany({ user: id });
  await User.deleteOne({ _id: id });

  console.log('User removed ' + id + '. For ' + user._id);
  return { success: true, message: 'Deleted' };
};

const copyUser = async (
  token: string,
  data: {
    id: string;
    email: string;
    password: string;
    firstName: string;
    lastName: string;
  },
): Promise<{
  success: boolean;
  message: string;
  errorCode?: number;
  data?: IUser;
}> => {
  const user = await User.getUserFromToken(token);
  if (!user) {
    return { success: false, message: 'User not found' };
  }

  if (user.role != 'admin') {
    console.log('Unauthorized to copy. ' + user._id);
    return { success: false, message: 'Unauthorized' };
  }

  if (!data.email || !data.password) {
    return { success: false, message: 'Missing fields' };
  }

  // Check if the email already exists
  const existingUser = await User.findOne({ email: data.email });
  if (existingUser) {
    return { success: false, message: 'Email already exists', errorCode: 1 };
  }

  // Fetch the user to copy
  const userToCopy = await User.findOne({ _id: data.id });
  if (!userToCopy) {
    return { success: false, message: 'No user to copy', errorCode: 1 };
  }

  const hash = await bcrypt.hash(data.password, 10);

  userToCopy.id = new ObjectId();
  userToCopy.isNew = true;
  userToCopy.email = data.email;
  userToCopy.password = hash;
  userToCopy.test = true;
  userToCopy.info = userToCopy.info || { profile: {} };
  userToCopy.info.profile = userToCopy.info.profile || {};
  userToCopy.info.profile.firstName = data.firstName;
  userToCopy.info.profile.lastName = data.lastName;
  await userToCopy.save();

  const measurements = await Measurement.find({ user: data.id });
  const measurementsToCopy: IMeasurement[] = [];
  for (const measurement of measurements) {
    measurement.id = new ObjectId();
    measurement.user = user.id;
    measurement.isNew = true;
    await measurement.save();
    measurementsToCopy.push(measurement);
  }

  const calendars = await Calendar.find({ user: data.id });
  const calendarsToCopy: ICalendar[] = [];
  for (const calendar of calendars) {
    calendar.id = new ObjectId();
    calendar.user = user.id;
    calendar.isNew = true;
    await calendar.save();
    calendarsToCopy.push(calendar);
  }

  userToCopy.measurements = measurementsToCopy;
  userToCopy.calendar = calendarsToCopy;
  await userToCopy.save();

  console.log('User copied: ' + userToCopy._id + '. For: ' + user._id);
  return { success: true, message: 'User copied', data: userToCopy };
};

const median = function (arr: number[]) {
  const mid = Math.floor(arr.length / 2),
    nums = [...arr].sort((a, b) => a - b);

  return arr.length % 2 !== 0 ? nums[mid] : nums[Math.max(0, mid - 1)];
};

const getCount = (calendar: ICalendar[], key: keyof CalendarDayData): number =>
  calendar.reduce((count, entry) => {
    const calendarData: CalendarDayData | null = entry.data;
    return count + (calendarData && calendarData[key] != null ? 1 : 0);
  }, 0);

const getMedianCycleCount = async (userId: string) => {
  const calendarItems = await Calendar.find({ user: userId }, {}, {}).sort({ date: 1 });

  const cycleLengths: number[] = [];
  let lastCycleStart: moment.Moment | null = null;

  for (let i = 0; i < calendarItems.length - 1; i++) {
    const current = calendarItems[i];
    const next = calendarItems[i + 1];

    const currentDate = moment(current.date);
    const nextDate = moment(next.date);

    const isCurrentPeriod = current.data?.period === true;
    const isNextPeriod = next.data?.period === true;

    if (isCurrentPeriod && !lastCycleStart) {
      lastCycleStart = currentDate;
    }

    const dayDifference = nextDate.diff(currentDate, 'days');

    if ((isCurrentPeriod && isNextPeriod && dayDifference > 1) || (!isCurrentPeriod && isNextPeriod)) {
      if (lastCycleStart) {
        const cycleLength = nextDate.diff(lastCycleStart, 'days');
        if (cycleLength > 0) {
          cycleLengths.push(cycleLength);
        }
      }
      lastCycleStart = nextDate;
    }
  }

  return median(cycleLengths);
};

const mapColumnToValue = (user: IUser, column: string, isAdmin: boolean): string => {
  const info = user.info || {};
  const profile = info.profile || {};

  switch (column) {
    case 'id':
      return user._id.toString() || '';
    case 'email':
      return isAdmin ? user.email || '' : '';
    case 'firstName':
      return isAdmin ? profile.firstName || '' : '';
    case 'lastName':
      return isAdmin ? profile.lastName || '' : '';
    case 'serial':
      return info.keggIdentifiers?.[0] || '';
    case 'lastUnfinishedCycleStart':
      return formatDate(info.lastUnfinishedCycleStart);
    case 'lastFinishedCycleLength':
      return info.lastFinishedCycleLength || '';
    case 'created':
      return formatDate(user.created);
    case 'lastActivity':
      return formatDate(user.lastActivity);
    case 'initialCycleLength':
      return profile.initialCycleLength || '';
    case 'initialStartOfPeriod':
      return profile.initialStartOfPeriod || '';
    case 'measurements':
      return user.measurements?.length.toString() || '';
    case 'cycles':
      return user.cycleCount.toString();
    case 'diary':
      return user.calendar?.length.toString() || '';
    case 'spotting':
      return getCount(user.calendar, 'spotting').toString();
    case 'sex':
      return getCount(user.calendar, 'sex').toString();
    case 'lhTest':
      return getCount(user.calendar, 'lhTest').toString();
    case 'progTest':
      return getCount(user.calendar, 'progTest').toString();
    case 'hcg':
      return getCount(user.calendar, 'pregnancy').toString();
    case 'temperature':
      return getCount(user.calendar, 'temperature').toString();
    case 'cervicalMucus':
      return getCount(user.calendar, 'cervicalMucus').toString();
    case 'sensation':
      return getCount(user.calendar, 'sensation').toString();
    case 'sexDrive':
      return getCount(user.calendar, 'sexDrive').toString();
    case 'infection':
      return getCount(user.calendar, 'infection').toString();
    case 'notes':
      return getCount(user.calendar, 'notes').toString();
    case 'brand':
      return info.deviceInfo?.brand || '';
    case 'model':
      return info.deviceInfo?.model || '';
    case 'os':
      return info.deviceInfo?.os || '';
    case 'platform':
      return info.deviceInfo?.platform || '';
    case 'appVersion':
      return info.appVersion || '';
    case 'lastKeggFirmwareVersion':
      return info.lastKeggFirmwareVersion || '';
    case 'lastMeasurementDate':
      return formatDate(info.lastMeasurementDate);
    case 'researchConsent':
      return profile.consent?.research?.toString() || '';
    case 'keggPlusConsent':
      return profile.consent?.keggPlus?.toString() || '';
    case 'ttcDuration':
      return profile.tryingToConceiveDuration || '';
    case 'medianCycleLength':
      return getMedianCycleCount(user._id.toString()).toString();
    case 'height':
      return profile.height || '';
    case 'weight':
      return profile.weight || '';
    case 'dateOfBirth':
      return formatDate(profile.dateOfBirth, true);
    case 'trackingReason':
      return profile.trackingReason || '';
    case 'regularCycle':
      return profile.regularCycle || '';
    case 'timeOfMeasurement':
      return profile.timeOfMeasurement || '';
    case 'language':
      return info.language || '';
    default:
      return '';
  }
};

const formatDate = (date?: string | Date | null, splitSlash: boolean = false): string => {
  if (date === null || date === undefined) return '';

  if (date instanceof Date) {
    date = date.toISOString();
  }

  if (splitSlash && typeof date === 'string' && date.includes('/')) {
    return date.split('/')[1] || date;
  }

  return moment(date).format('YYYY-MM-DD HH:mm:ss') || '';
};

const exportUser = async (token: string, columns: string[]): Promise<string> => {
  const user = await User.getUserFromToken(token);

  if (!user || !['admin', 'support', 'readonly'].includes(user.role)) {
    throw new Error('Unauthorized');
  }

  const isAdmin = user.role == 'admin';

  const query: {
    role: string;
    test: { $ne: boolean };
    enabled: { $eq: boolean };
    group?: string;
  } = { role: 'user', test: { $ne: true }, enabled: { $eq: true } };
  if (user.group != null && user.group.length > 0) {
    query.group = user.group;
  }

  let users: IUser[] = [];
  if (
    columns.some((col) =>
      [
        'spotting',
        'sex',
        'lhTest',
        'progTest',
        'hcg',
        'temperature',
        'cervicalMucus',
        'sensation',
        'sexDrive',
        'infection',
        'notes',
      ].includes(col),
    )
  ) {
    users = await User.aggregate([
      {
        $lookup: {
          from: 'calendars',
          localField: '_id',
          foreignField: 'user',
          as: 'calendar',
        },
      },
      { $match: query },
    ]);
  } else {
    users = await User.find(query);
  }

  columns.unshift('id');
  let csvData = columns.join(',') + '\n';

  for (const user of users) {
    const rowData = columns.map((column) => mapColumnToValue(user, column, isAdmin)).join(',');
    csvData += rowData + '\n';
  }

  console.log('User export successful. For user: ' + user._id);
  return csvData;
};

const sendToken = async (
  email: string,
  password: string,
  userToken: string,
): Promise<{
  success: boolean;
  error?: string;
}> => {
  const user = await User.authenticate(email, password);

  if (user.phone == null || user.phone.length == 0) {
    return await login(email, password, userToken);
  }

  const token = Math.floor(100000 + Math.random() * 900000).toString();
  user.token = token;
  await user.save();
  await sendTokenBySMS(user.phone, token);

  return { success: true };
};

const verifyEmail = async (
  token: string,
): Promise<{
  success: boolean;
  message: string;
}> => {
  return { success: true, message: 'Ok' };

  //   const user = await User.getUserFromToken(token);

  //   if (user.emailVerified === false) {
  //     return { success: false, message: 'Email not verified' };
  //   } else {
  //     return { success: true, message: 'Ok' };
  //   }
};

const confirmEmail = async (
  email: string,
): Promise<{
  success: boolean;
  message: string;
}> => {
  const user = await User.findOne({ email });
  if (user != null) {
    user.emailVerified = true;
    user.save();
    return { success: true, message: 'Ok' };
  } else {
    return { success: false, message: 'User does not exist' };
  }
};

const getPredictions = async (
  token: string,
  userId: string,
): Promise<{
  success: boolean;
  message?: string;
  confirmation?: OvulationData;
  prediction?: ForecastData;
}> => {
  const baseUrl = Consts.predictionUrl;
  const isDev = Consts.isDev ? 'true' : 'false';

  const user = await User.getUserFromToken(token);

  if (user.role != 'admin' && user.role != 'support' && user.role != 'readonly') {
    return { success: false, message: 'Unauthorized' };
  }
  if (!userId) {
    return { success: false, message: 'Missing fields' };
  }

  const confirmationUrl = `${baseUrl}/confirm/ovulation?dev=${isDev}&user=${userId}&after=${moment()
    .subtract(300, 'days')
    .format('YYYY-MM-DD')}`;
  const predictionUrl = `${baseUrl}/prediction/forecast?dev=${isDev}&user=${userId}`;

  const [confirmation, prediction] = await Promise.all([
    ofetch(confirmationUrl, { method: 'GET', parseResponse: JSON.parse }),
    ofetch(predictionUrl, { method: 'GET', parseResponse: JSON.parse }),
  ]);

  return { success: true, confirmation, prediction };
};

const sendResetPasswordLink = async (
  email: string,
): Promise<{
  success: boolean;
  message?: string;
}> => {
  if (!email) {
    return { success: false, message: 'Missing fields' };
  }

  const user = await User.findOne({ email });
  if (!user) {
    return { success: false, message: 'User not found' };
  }

  const token = crypto.randomBytes(32).toString('hex');
  user.tempPasswordToken = token;
  user.tempPasswordTokenValidity = moment().add(1, 'hours').toDate();
  await user.save();

  const host = Consts.isDev ? 'https://api.kegg.tech:8081' : 'https://api.kegg.tech:8888';
  const link = `${host}/user/resetPassword?email=${encodeURIComponent(user.email)}&token=${token}`;

  let body = 'Hey there!,';
  body += '\n<br/>';
  body += "Forgot your password? We've got you covered.\n</br>";
  body += 'Your unique password reset link will be available for 1 hour.\n<br/>';
  body += `<a href="${link}">Reset my password</a>`;
  body += '\n<br/>';
  body += '\n<br/>';
  body += "Please contact kegg's user support if you need further assistance.\n<br/>";
  body += '<a href="http://kegg.tech/support">User support</a>';

  Emailer.send([email], 'Reset Your kegg Password', body, undefined, () => {});

  console.log('Password reset link sent for: ' + user._id);
  return { success: true };
};

const resetPassword = async (
  email: string,
  token: string,
  newPassword: string,
): Promise<{
  success: boolean;
  message: string;
}> => {
  const user = await User.findOne({ email, tempPasswordToken: token });

  if (!user || moment().isAfter(moment(user.tempPasswordTokenValidity)) || newPassword.length < 8) {
    console.log('User change password fail' + user?._id);
    return { success: false, message: 'Could not change password' };
  }

  user.password = await bcrypt.hash(newPassword, 10);
  user.tempPasswordToken = null;
  user.tempPasswordTokenValidity = null;
  await user.save();

  console.log('User change password: ' + user._id);
  return { success: true, message: 'Password changed successfully' };
};

// TEMPORARY TRANSITIONAL FUNCTIONS -------------------------------------------------------

// Login V

const requestLoginLink = async (email: string): Promise<{ success: boolean; message?: string }> => {
  try {
    const users = await User.find({ email: { $regex: new RegExp(`^${email.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`, 'i') } });
    if(users.length > 1) {
      console.log("RequestLoginLink Warning: Multiple users found for email: " + email);
      return { success: false, message: 'Multiple users found' };
    }
    else if (users.length <= 0) {
      console.log("RequestLoginLink Warning: No users found for email: " + email);
      return { success: false, message: 'User not found' };
    }
    
    const user = users[0];

    // Check if the user's email has different casing and update it if necessary
    if (user && user.email !== email && user.email.toLowerCase() === email.toLowerCase()) {
      console.log(`User email has different casing: ${user.email} vs requested ${email}, updating...`);
      user.email = email;
      await user.save();
    }

    const token = crypto.randomBytes(64).toString('hex');
    user.tempPasswordToken = token;
    user.tempPasswordTokenValidity = moment().add(1, 'hours').toDate();
    await user.save();

    const loginLink = `${Consts.frontendUrl}${token}`;
    
    const emailBody = `
      <head>
<meta charset="utf-8" />
<title>Email Template</title>
</head>
<body style="margin:0;padding:0;border:0;font:inherit;font-size:100%;line-height:1.4;-webkit-font-smoothing:antialiased;box-sizing:border-box;height:100%;background-color:#ffffff;">

<table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" bgcolor="#ffffff" style="border-collapse:collapse;width:100%;background-color:#ffffff;">
  <!-- LOGO -->
  <tr>
    <td align="center" style="padding-top:60px;padding-bottom:40px;">
      <picture>
        <source srcset="https://static-content.kegg.tech/img/logo-dark.png" media="(prefers-color-scheme: dark)" />
        <img src="https://static-content.kegg.tech/img/logo.png" alt="kegg logo" width="142" height="40" style="display:block;border:0;outline:none;text-decoration:none;" />
      </picture>
    </td>
  </tr>

  <tr>
    <td align="center">
      <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="600" style="width:600px;max-width:100%;">
        <tr>
          <td align="center" style="padding:40px 32px;background:#f8f3e9;border-radius:16px;">
            <!-- Greeting -->
            <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
              <tr>
                <td style="font-family:'Inter-Bold',Helvetica,Arial,sans-serif;font-size:20px;font-weight:700;color:#000000;line-height:24px;text-align:center;padding-bottom:24px;">
                  Hi there!
                </td>
              </tr>
              <tr>
                <td style="font-family:'Inter-Regular',Helvetica,Arial,sans-serif;font-size:16px;font-weight:400;color:#000000;line-height:24px;text-align:center;padding-bottom:24px;">
                  Your secure login link will expire in 1&nbsp;hour and can only be used once.<br>Click below to sign&nbsp;in:
                </td>
              </tr>

              <!-- BUTTON -->
              <tr>
                <td align="center" style="padding-bottom:24px;">
                  <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="margin:auto;">
                    <tr>
                      <td align="center" bgcolor="#fc5200" style="border-radius:100px;">
                        <a href="${loginLink}" style="display:inline-block;padding:16px 32px;font-family:'Inter-Bold',Helvetica,Arial,sans-serif;font-size:16px;font-weight:700;letter-spacing:1px;text-decoration:none;color:#f7f3e9;border-radius:100px;background-color:#fc5200;">
                          CLICK&nbsp;TO&nbsp;LOGIN
                        </a>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
            <!-- END inner table -->
          </td>
        </tr>
      </table>
    </td>
  </tr>

  <!-- SPACER -->
  <tr><td style="height:32px;font-size:0;line-height:0;">&nbsp;</td></tr>

  <tr>
    <td align="center">
      <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="600" style="width:600px;max-width:100%;">
        <tr>
          <td align="center" style="padding:24px 32px;background:#ffffff;border-radius:16px;font-family:'Inter-Regular',Helvetica,Arial,sans-serif;font-size:16px;color:#000000;line-height:24px;text-align:center;">
            Didn't request this? You can safely ignore this email.
          </td>
        </tr>
      </table>
    </td>
  </tr>

  <tr><td style="height:32px;font-size:0;line-height:0;">&nbsp;</td></tr>

  <tr>
    <td align="center" style="padding-bottom:40px;">
      <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="600" style="width:600px;max-width:100%;">
        <tr>
          <td align="center" style="background:#f8f3e9;padding:40px 32px;border-radius:16px;">
            <!-- Tagline -->
            <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
              <tr>
                <td style="font-family:'Inter-SemiBold',Helvetica,Arial,sans-serif;font-size:18px;font-weight:600;color:#211e20;text-align:center;letter-spacing:0.18px;line-height:28px;padding-bottom:20px;">
                  #FertilityUnderstood
                </td>
              </tr>

              <tr>
                <td style="font-family:'Inter-Regular',Helvetica,Arial,sans-serif;font-size:12px;color:#898989;text-align:center;line-height:16px;padding-bottom:18px;">
                  <span style="font-weight:700;color:#3b393d;">Lady&nbsp;Technologies&nbsp;Inc.</span>,
                  <br />2565&nbsp;3rd&nbsp;St, Suite&nbsp;318, San&nbsp;Francisco, CA&nbsp;94107
                  <br /><br />
                  To make sure you keep getting these emails, please add
                  <a href="mailto:<EMAIL>" style="color:#3b393d;text-decoration:underline;"><EMAIL></a>
                  to your address book or whitelist us. Want out of the loops?
                  <br /><br />
                  *12&nbsp;month Pregnancy Guarantee Terms&nbsp;&amp;&nbsp;Conditions apply.
                </td>
              </tr>

              <!-- Social Icons -->
              <tr>
                <td align="center" style="padding-bottom:8px;">
                  <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="margin:auto;">
                    <tr>
                      <td style="padding:0 8px;">
                        <a href="https://www.facebook.com/kegg.tech" style="display:inline-block;background:#ffffff;border-radius:24px;padding:8px;">
                          <img src="https://static-content.kegg.tech/img/facebook.png" alt="Facebook" width="24" height="24" style="display:block;border:0;" />
                        </a>
                      </td>
                      <td style="padding:0 8px;">
                        <a href="https://www.instagram.com/kegg_tech" style="display:inline-block;background:#ffffff;border-radius:24px;padding:8px;">
                          <img src="https://static-content.kegg.tech/img/instagram.png" alt="Instagram" width="24" height="24" style="display:block;border:0;" />
                        </a>
                      </td>
                      <td style="padding:0 8px;">
                        <a href="https://www.tiktok.com/@kegg_tech" style="display:inline-block;background:#ffffff;border-radius:24px;padding:8px;">
                          <img src="https://static-content.kegg.tech/img/tiktok.png" alt="TikTok" width="24" height="24" style="display:block;border:0;" />
                        </a>
                      </td>
                      <td style="padding:0 8px;">
                        <a href="https://www.youtube.com/@kegg_tech" style="display:inline-block;background:#ffffff;border-radius:24px;padding:8px;">
                          <img src="https://static-content.kegg.tech/img/youtube.png" alt="YouTube" width="24" height="24" style="display:block;border:0;" />
                        </a>
                      </td>
                      <td style="padding:0 8px;">
                        <a href="https://www.pinterest.com/kegg_tech" style="display:inline-block;background:#ffffff;border-radius:24px;padding:8px;">
                          <img src="https://static-content.kegg.tech/img/pinterest.png" alt="Pinterest" width="24" height="24" style="display:block;border:0;" />
                        </a>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
            <!-- END footer inner -->
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>

</body>
    `;

    Emailer.send([user.email], 'Your Login Link', emailBody, undefined, () => {});
    return { success: true };
  } catch (error) {
    console.error('Error requesting login link:', error);
    return { success: false, message: 'An error occurred while requesting the login link' };
  }
};

const verifyGoogleToken = async (token: string) => {
  const client = new OAuth2Client();
  return client.verifyIdToken({
    idToken: token,
    audience: Consts.googleClientID.split(',').map(item => item.trim()).filter(item => item.length > 0),
  });
};

const handleExternalLogin = async (
  identityToken: string,
  type: 'apple' | 'google',
  language: string
): Promise<{ user: IUser, freshCreated: boolean } | null> => {
  let email: string | null = null;
  let name: string | null = null;
  let freshCreated = false;

  try {
    if (type === 'apple') {
      const { email: appleEmail } = await verifyAppleToken({
        idToken: identityToken,
        clientId: Consts.appleClientID!,
      });
      email = appleEmail;
    } else if (type === 'google') {
      const ticket = await verifyGoogleToken(identityToken);
      const payload = ticket.getPayload();
      email = payload?.email ?? null;
      name = payload?.name ?? null;
    }
  } catch (error) {
    console.error('SSO Verification Error:', error);
    return null;
  }

  if (!email) return null;

  let user = await User.findOne({ email });
  if (!user) {
    const tempPassword = crypto.randomBytes(64).toString('hex');
    const hash = await bcrypt.hash(tempPassword, 10);
    freshCreated = true;
    user = await User.create({
      email,
      password: hash,
      role: 'user',
      language,
      info: {
        profile: {
          firstName: name?.split(' ')[0] || '',
          lastName: name?.split(' ')[1] || '',
        }
      }
    });
  } else if (name) {
    user.info = user.info || {};
    user.info.profile = user.info.profile || {};
    user.info.profile.firstName = name.split(' ')[0];
    user.info.profile.lastName = name.split(' ')[1] || '';
    await user.save();
  }

  return { user, freshCreated };
};

const signUpV2 = async ({
  authorizationType,
  identityToken,
  language
}: SignUpV2Params): Promise<{
  success: boolean;
  message: string;
  token?: string;
  user?: IUser;
  freshCreated?: boolean;
}> => {
  try {
    if (authorizationType === 'email') {
      const existingUser = await User.findOne({ email: identityToken.toLowerCase() });
      if (existingUser) {
        return { success: false, message: 'User already exists'};
      }

      const tempPassword = crypto.randomBytes(64).toString('hex');
      const hash = await bcrypt.hash(tempPassword, 10);
      const newUser = await User.create({
        email: identityToken.toLowerCase(),
        password: hash,
        role: 'user',
        language,
      });

      await requestLoginLink(newUser.email);
      klaviyoSendProfile(newUser.email);
      klaviyoSendEvent(newUser.email, 'kegg-app-signup-email', "User Signed up in kegg app using Email");
      return { success: true, message: 'Check your email for login link' };
    }

    const externalLoginResult = await handleExternalLogin(identityToken, authorizationType, language);
    if (!externalLoginResult) return { success: false, message: 'Authentication failed' };
    const { user, freshCreated } = externalLoginResult;

    if(freshCreated) {
      klaviyoSendProfile(user.email);
      klaviyoSendEvent(user.email, `kegg-app-signup-sso-${authorizationType}`, `User Signed up in kegg app using ${authorizationType.toUpperCase()}`);
    }
    const token = await User.makeToken(user._id.toString());
    return { success: true, message: 'Logged in', token, user, freshCreated };
  } catch (error) {
    console.error('Signup V2 Error:', error);
    return { success: false, message: 'Authentication error' };
  }
};

const loginV2 = async ({
  authorizationType,
  identityToken,
  language
}: LoginV2Params): Promise<{
  success: boolean;
  message: string;
  token?: string;
  user?: IUser;
  freshCreated?: boolean;
}> => {
  try {
    if (authorizationType === 'email') {
      const user = await User.findOne({ 
        tempPasswordToken: identityToken,
        tempPasswordTokenValidity: { $gt: new Date() }
      });
      
      if (!user) return { success: false, message: 'Invalid or expired token' };

      user.tempPasswordToken = null;
      user.tempPasswordTokenValidity = null;
      await user.save();

      const token = await User.makeToken(user._id.toString());
      return { success: true, message: 'Logged in', token, user };
    }

    const externalLoginResult = await handleExternalLogin(identityToken, authorizationType, language || 'en');
    if (!externalLoginResult || !externalLoginResult.user) return { success: false, message: 'Authentication failed' };
    const { user, freshCreated } = externalLoginResult;

    if(freshCreated) {
      klaviyoSendProfile(user.email);
      klaviyoSendEvent(user.email, `kegg-app-login-sso-${authorizationType}`, `User logged in in kegg app using ${authorizationType.toUpperCase()}`);
    }

    const token = await User.makeToken(user._id.toString());
    return { success: true, message: 'Logged in', token, user, freshCreated };
  } catch (error) {
    console.error('Login V2 Error:', error);
    return { success: false, message: 'Authentication error' };
  }
};

const mapLegacyTrackingReasonToV2 = (legacy?: string): TrackingReasonV2 => {
  if (!legacy) return [];
  switch (legacy) {
    case 'trying_to_conceive':
      return ['getPregnant'];
    case 'cycle_info_tracking_fertility':
      return ['trackMyCycle'];
    case 'not_sure_yet':
      return ['exerciseMyPelvicFloor'];
    default:
      return [];
  }
};


export const getUserV2 = async (
  token: string
): Promise<{ success: boolean; message: string; data?: UserDataV2 }> => {
  const user: IUser | null = await User.getUserFromToken(token);
  if (!user) {
    return { success: false, message: 'User not found' };
  }

  const profile = user.info?.profile || {};

  // Map the legacy dateOfBirth: if only a year (YYYY) is stored, add a default month/day.
  let birthDate = '';
  if (profile.dateOfBirth) {
    birthDate = /^\d{4}$/.test(profile.dateOfBirth)
      ? `${profile.dateOfBirth}-01-01`
      : profile.dateOfBirth;
  }

  // Use the new trackingReasonV2 if available, otherwise map from the legacy trackingReason.
  let trackingReason: TrackingReasonV2 = [];
  if (profile.trackingReasonV2 && Array.isArray(profile.trackingReasonV2) && profile.trackingReasonV2.length > 0) {
    trackingReason = profile.trackingReasonV2;
  } else if (profile.trackingReason) {
    trackingReason = mapLegacyTrackingReasonToV2(profile.trackingReason);
  }

  const result: UserDataV2 = {
    id: user._id.toString(),
    email: user.email,
    firstName: profile.firstName || '',
    lastName: profile.lastName || '',
    birthDate,
    height: profile.height || '',
    weight: profile.weight || '',
    consent: profile.consent || {},
    trackingReason,
    cycleType: profile.regularCycle ? (profile.regularCycle === 'regular' ? 'regular' : 'irregular') : undefined,
    initialCycleLength: profile.initialCycleLength || '',
    initialPeriodLength: profile.initialPeriodLength || '',
    conditions: profile.conditions || [],
    lastUnfinishedCycleStart: user.info?.lastUnfinishedCycleStart || '',
    medications: profile.medications || [],
    emailVerified: user.emailVerified,
    keggIdentifiers: user.info?.keggIdentifiers ? user.info.keggIdentifiers[0] : '',
    profileVersion: profile.profileVersion || 1,
  };

  return { success: true, message: 'User retrieved successfully', data: result };
};

export const updateUserV2 = async (
  token: string,
  data: UserDataV2,
  mixpanelApi: Mixpanel.Mixpanel | null
): Promise<{ success: boolean; message: string; data?: UserDataV2 }> => {
  const currentUser: IUser | null = await User.getUserFromToken(token);
  if (!currentUser) {
    return { success: false, message: 'Invalid token' };
  }

  // Only allow admins/support to update other users; everyone else updates their own record.
  let targetUserId = data.id;
  if (!['admin', 'support'].includes(currentUser.role)) {
    targetUserId = currentUser._id.toString();
  }

  const userToUpdate: IUser | null = await User.findOne({ _id: targetUserId });
  if (!userToUpdate) {
    return { success: false, message: 'User not found' };
  }

  // Update top-level fields (email)
  if (data.email) {
    userToUpdate.email = data.email;
  }

  // Ensure that info and profile exist
  let isNewUser = false;
  if (!userToUpdate.info) {
    isNewUser = true;
  }

  userToUpdate.info = userToUpdate.info || {};
  userToUpdate.info.profile = userToUpdate.info.profile || {};
  const profile = userToUpdate.info.profile;

  // Update profile fields
  profile.firstName = data.firstName || profile.firstName;
  profile.lastName = data.lastName || profile.lastName;
  profile.height = data.height || profile.height;
  profile.weight = data.weight || profile.weight;
  profile.consent = data.consent || profile.consent;
  profile.dateOfBirth = data.birthDate || profile.dateOfBirth;

  // Save new tracking reasons into trackingReasonV2 and map to legacy trackingReason.
  if (data.trackingReason && data.trackingReason.length > 0) {
    profile.trackingReasonV2 = data.trackingReason;
    if (data.trackingReason.includes('getPregnant')) {
      profile.trackingReason = 'trying_to_conceive';
    } else if (data.trackingReason.includes('trackMyCycle')) {
      profile.trackingReason = 'cycle_info_tracking_fertility';
    } else {
      profile.trackingReason = null;
    }
  }

  if (data.cycleType) {
    profile.cycleType = data.cycleType;
    // Map cycle type (legacy field "regularCycle" accepts 'regular' or 'iregular' - yes, there is a typo)
    // Mapping only for the legacy Insight
    profile.regularCycle = data.cycleType === 'regular' ? 'regular' : 'iregular';
  }
  profile.initialCycleLength = data.initialCycleLength || profile.initialCycleLength;
  profile.initialPeriodLength = data.initialPeriodLength || profile.initialPeriodLength;
  profile.conditions = data.conditions || profile.conditions;
  profile.medications = data.medications || profile.medications;

  if (
    (profile.profileVersion == undefined || profile.profileVersion == null || profile.profileVersion == 1) &&
    !isNewUser
  ) {
    klaviyoSendEvent(userToUpdate.email, 'app-migrated-2.0', "App Migrated to the new Version");
  }
  profile.profileVersion = data.profileVersion || 2;

  // Update fields stored at the info level
  if (data.lastUnfinishedCycleStart) {
    userToUpdate.info.lastUnfinishedCycleStart = data.lastUnfinishedCycleStart;
  }
  if (typeof data.emailVerified === 'boolean') {
    userToUpdate.emailVerified = data.emailVerified;
  }
  if (data.keggIdentifiers) {
    userToUpdate.info.keggIdentifiers = [data.keggIdentifiers];
  }

  userToUpdate.markModified('info');

  await userToUpdate.save();

  // Build the updated transitional user object to return
  const updatedUser: UserDataV2 = {
    id: userToUpdate._id.toString(),
    email: userToUpdate.email,
    firstName: profile.firstName || '',
    lastName: profile.lastName || '',
    birthDate: profile.dateOfBirth || '',
    height: profile.height || '',
    weight: profile.weight || '',
    consent: profile.consent || {},
    trackingReason: profile.trackingReasonV2 || [],
    cycleType: profile.cycleType || undefined,
    initialCycleLength: profile.initialCycleLength || '',
    initialPeriodLength: profile.initialPeriodLength || '',
    conditions: profile.conditions || [],
    lastUnfinishedCycleStart: userToUpdate.info.lastUnfinishedCycleStart || '',
    medications: profile.medications || [],
    emailVerified: userToUpdate.emailVerified,
    keggIdentifiers: userToUpdate.info.keggIdentifiers ? userToUpdate.info.keggIdentifiers[0] : '',
    profileVersion: profile.profileVersion || 1,
  };

  return { success: true, message: 'User updated successfully', data: updatedUser };
};
//END OF TEMPORARY TRANSITIONAL FUNCTIONS -------------------------------------------------------

const protectedKlaviyoSendEvent = async (
  token: string,
  eventAction: string,
  eventName: string,
  userProperties: Record<string, string> = {}
): Promise<{ success: boolean; message: string }> => {
  try {
    const user = await User.getUserFromToken(token);
    if (!user) {
      return { success: false, message: 'Invalid token' };
    }

    if (!user.email) {
      return { success: false, message: 'User email not found' };
    }

    await klaviyoSendEvent(user.email, eventAction, eventName, userProperties);
    return { success: true, message: 'Event sent successfully' };
  } catch (error) {
    console.error('Error in protectedKlaviyoSendEvent:', error);
    return { success: false, message: 'Failed to send event' };
  }
};

export default {
  getStatistics,
  getPredictionData,
  getUsers,
  signUp,
  login,
  verifyUser,
  createUser,
  updateUser,
  changePassword,
  removeUser,
  copyUser,
  exportUser,
  sendToken,
  verifyEmail,
  confirmEmail,
  getPredictions,
  sendResetPasswordLink,
  resetPassword,
  requestLoginLink,
  signUpV2,
  loginV2,
  getUserV2,
  updateUserV2,
  klaviyoSendProfile,
  klaviyoSendEvent,
  protectedKlaviyoSendEvent,
};
