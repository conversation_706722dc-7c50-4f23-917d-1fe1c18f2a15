import { Consts } from '@/consts';
import Calendar from '@/models/calendar';
import ExternalConnection from '@/models/externalConnection';
import User from '@/models/user';
import { CalendarDayData, TemperatureType } from '@/types/calendar';
import { IExternalConnection } from '@/types/externalConnection';
import { OutgoingHttpHeaders } from 'http';
import https from 'https';
import moment from 'moment';
import Measurement from '@/models/measurement';

const getExternalConnections = async (
  token: string,
): Promise<{
  success: boolean;
  connections?: IExternalConnection[];
  message?: string;
}> => {
  const user = await User.getUserFromToken(token);

  if (!user) {
    throw new Error('User not found');
  }

  const connections = await ExternalConnection.find({ user: user._id });

  return { success: true, connections };
};

interface UserDataResponse {
  userData: {
    time: string;
    score: string;
  }[];
}

type SendResponse<T> = {
  statusCode: number;
  data: T;
};

const send = <T>(
  hostname: string,
  path: string,
  type: string,
  data: unknown | null,
  headers: OutgoingHttpHeaders | null = null,
): Promise<SendResponse<T>> => {
  return new Promise((resolve, reject) => {
    let encodedData = null;

    if (data) {
      encodedData = new TextEncoder().encode(JSON.stringify(data));
    }

    const options: https.RequestOptions = {
      hostname: hostname,
      path: path,
      method: type,
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': encodedData ? encodedData.length : 0,
        ...headers,
      },
    };

    const req = https.request(options, (res) => {
      let str = '';
      res.on('data', function (chunk) {
        str += chunk;
      });

      res.on('end', function () {
        let json = null;
        try {
          json = JSON.parse(str);
        } catch {
          console.log('Error parsing JSON response');
        }
        resolve({ statusCode: res.statusCode!, data: json });
      });
    });

    req.on('error', (error) => {
      console.error(error);
      reject(error);
    });

    if (encodedData) {
      req.write(encodedData);
    }

    req.end();
  });
};

const createExternalConnection = async (token: string, type: string, code: string) => {
  const user = await User.getUserFromToken(token);

  const params = {
    client_id: Consts.tempDropClientID,
    client_secret: Consts.tempDropClientSecret,
    code: code,
    grant_type: 'authorization_code',
  };

  const result = await send('tdauth.tempdrop.com', '/auth/token', 'POST', params);

  if (result.statusCode === 200) {
    // Delete existing connections for the user
    await ExternalConnection.deleteMany({ user: user._id });

    // Create a new connection
    const obj = {
      user: user._id,
      created: new Date(),
      lastSynchronization: null,
      type: type,
      data: result.data,
    };

    const connection = await ExternalConnection.create(obj);
    console.log('External connection ' + obj.type + ' created for ' + user._id);

    return { success: true, message: 'Ok', connection };
  } else {
    console.log('External connection fail: ' + result.data, "Params:", params);
    return { success: false, message: result.data };
  }
};

const synchronizeExternalConnection = async (userId: string, connectionId: string, unit: string) => {
  // Find the specific connection by ID and user
  const connection = await ExternalConnection.findOne({ _id: connectionId, user: userId });

  if (!connection) {
    throw new Error('Connection not found');
  }

  // Get time offset from last measurement for timezone handling
  let diffHours = 0;
  const lastMeasurement = await Measurement.findOne(
    { user: userId },
    {},
    { sort: { 'serverDate': -1 } }
  );

  if (lastMeasurement) {
    const date = moment.utc(lastMeasurement.date);
    const serverDate = moment.utc(lastMeasurement.serverDate);
    diffHours = Math.round(date.diff(serverDate, 'minutes') / 60.0);
  }

  const dateFormat = 'YYYY-MM-DD[T]HH:mm:ss.SSS[Z]';
  const now = moment.utc();
  let startTime = now.clone().subtract(178, 'days');
  const endTime = now;

  // Refresh token first
  const refreshParams = {
    client_id: Consts.tempDropClientID,
    client_secret: Consts.tempDropClientSecret,
    refresh_token: (connection.data as any).refresh_token ?? '',
    grant_type: 'refresh_token'
  };

  const refreshResult = await send<any>(
    'tdauth.tempdrop.com',
    '/auth/token',
    'POST',
    refreshParams,
    null
  );

  if (refreshResult.statusCode === 200) {
    connection.data = refreshResult.data;
    await connection.save();
  }

  // Adjust start time based on last synchronization (5 days buffer in updated code)
  if (connection.lastSynchronization) {
    const lastSynchronization = moment.utc(connection.lastSynchronization).subtract(5, 'days');
    if (lastSynchronization.isAfter(startTime)) {
      startTime = lastSynchronization;
    }
  }

  const startTimeString = encodeURIComponent(startTime.format(dateFormat));
  const endTimeString = encodeURIComponent(endTime.format(dateFormat));
  const headers = { 'Authorization': 'Bearer ' + connection.data["access_token"] };

  // Call the updated API endpoint
  const result = await send<UserDataResponse>(
    'tdauth.tempdrop.com',
    `/api/v1/userdata?startTime=${startTimeString}&endTime=${endTimeString}&timeZoneSeconds=0`,
    'GET',
    null,
    headers
  );

  const ret: {
    date: string | null;
    data?: CalendarDayData | null;
    values?: number[];
    lastMeasurementTime?: string;
  }[] = [];

  if (result.statusCode !== 200 || !result.data || !result.data.userData) {
    console.log('External connection sync fail: ' + connection._id);
    throw new Error('Sync failed');
  }

  // Update the synchronization time
  connection.lastSynchronization = now.toDate();
  await connection.save();

  // Process the measurements
  for (const measurement of result.data.userData) {
    let dayString = null;
    const time = measurement['time'];
    let score: number = parseFloat(measurement['score']);

    // Convert units if needed
    if (unit !== 'C') {
      score = (score * 9) / 5 + 32;
    }

    const temperatureObj: TemperatureType = { 
      value: parseFloat(score.toFixed(2)), 
      source: connection.type 
    };

    if (time && !isNaN(score)) {
      // Apply timezone adjustment from the updated code
      dayString = moment.utc(time).add(diffHours, 'hours').format('YYYY-MM-DD');
    }

    const r: { 
      date: string | null; 
      data?: CalendarDayData | null; 
      values?: number[]; 
      lastMeasurementTime?: string 
    } = {
      date: dayString,
    };

    // Check if the calendar entry already exists
    const existing = await Calendar.findOne({ user: userId, date: dayString });

    if (existing) {
      const data = existing.data || {};
      data.temperature = temperatureObj;
      
      // Clear and reassign to ensure proper update (as in the JS code)
      existing.data = null;
      existing.data = data;
      
      await existing.save();
      r.data = existing.data;
      r.values = existing.values;
      r.lastMeasurementTime = existing.lastMeasurementTime;
    } else {
      const calendarItem = {
        user: userId,
        date: moment.utc(dayString),
        type: '',
        data: { temperature: temperatureObj },
      };
      
      const item = await Calendar.create(calendarItem);
      r.data = item.data;
    }

    ret.push(r);
  }

  return ret;
};


const removeExternalConnection = async (token: string, connectionId: string) => {
  if (!connectionId) {
    throw new Error('Missing fields');
  }

  const user = await User.getUserFromToken(token);
  if (!user) {
    throw new Error('Invalid user token');
  }

  const toRemove = await ExternalConnection.findOne({ user: user._id, _id: connectionId });
  if (!toRemove) {
    throw new Error('External connection does not exist');
  }

  await ExternalConnection.deleteOne({ _id: toRemove._id });
  console.log('External connection removed: ' + toRemove._id);

  return { success: true, message: 'Ok' };
};

export default {
  getExternalConnections,
  createExternalConnection,
  synchronizeExternalConnection,
  removeExternalConnection,
};
