import { Consts } from '@/consts';
import Calendar from '@/models/calendar';
import Measurement from '@/models/measurement';
import User from '@/models/user';
import { IMeasurement, KeggInfo, MeasurementData } from '@/types/measurements';
import { IUser } from '@/types/user';
import Emailer from '@/utils/emailer';
import * as math from 'mathjs';
import moment from 'moment';
import klaviyoSendEvent from '@/services/user_service';
import user_service from '@/services/user_service';

type GetMeasurementsQuery = {
  _id?: string;
  user?: string;
  appVersion?: {
    $regex: string;
    $options: string;
  };
  firmwareVersion?: {
    $regex: string;
    $options: string;
  };
  date?: {
    $gte: number;
    $lte: number;
  };
  lastMeasurement?: boolean;
  resultCode?: {
    $not: {
      $regex: string;
    };
  };
};

type MeasurementDTO = {
  _id: string;
  keggId: string;
  serverDate: Date;
  data: MeasurementData[];
  date: Date;
  protocolVersion: string;
  measurementValue: number;
  appVersion: string;
  firmwareVersion: string;
  phoneInfo: {
    platform: string;
    os: string;
    brand: string;
    model: string;
  };
  user: string;
  keggInfos: object[];
  sequenceInfo: object;
  resultCode: string;
  session: object[];
  computedValues: object;
  temperature: number | null;
};

const getMeasurements = async (
  query: GetMeasurementsQuery,
  page: number,
  pagesize: number,
  measurements: IMeasurement[],
): Promise<{
  data: MeasurementDTO[];
  pagination: {
    page: number;
    pagesize: number;
    total: number;
  };
}> => {
  const total = await Measurement.countDocuments(query);

  const data: MeasurementDTO[] = measurements.map((measurement) => ({
    _id: measurement._id.toString(),
    keggId: measurement.keggId,
    serverDate: measurement.serverDate,
    data: measurement.data,
    date: measurement.date,
    protocolVersion: measurement.protocolVersion,
    measurementValue: measurement.measurementValue,
    appVersion: measurement.appVersion,
    firmwareVersion: measurement.firmwareVersion,
    phoneInfo: measurement.phoneInfo,
    user: measurement.user.toString(),
    keggInfos: measurement.keggInfos,
    sequenceInfo: measurement.sequenceInfo,
    resultCode: measurement.resultCode,
    session: measurement.session,
    computedValues: measurement.computedValues,
    temperature: measurement.temperature,
  }));

  return {
    data,
    pagination: {
      page,
      pagesize,
      total,
    },
  };
};

type AddMeasurementData = {
  date: Date;
  rawKeggInfos: {
    date: Date;
    info: Uint8Array;
  }[];
  rawKeggInfo: Uint8Array;
  keggId: string;
  phoneInfo: {
    platform: string;
    os: string;
    brand: string;
    model: string;
  };
  sequenceInfo: object;
  resultCode: string;
  protocolVersion: string;
  appVersion: string;
  data: Uint8Array[];
  session: object[];
};

const inRange = (value: number) => {
  return value > Consts.minimalAcceptedResistance && value < Consts.maximalAcceptedResistance;
};

const parseKeggInfos = (rawKeggInfos: { date: Date; info: Uint8Array }[]) => {
  const keggInfos = [];

  let lastKeggFirmwareVersion = '';
  for (const rawInfo of rawKeggInfos) {
    const keggInfo: KeggInfo = {
      date: rawInfo.date,
      raw: rawInfo.info,
    };

    try {
      const info = proto.KegelInfo.deserializeBinary(rawInfo.info);
      keggInfo.battery = info.getBatteryPercentage();
      const fw = info.getFirmwareVersion() as proto.KegelInfo.FirmwareVersion;
      keggInfo.fw = fw.getMajor() + '.' + fw.getMinor() + '.' + fw.getPoint();
      keggInfo.bl = info.getBootloaderVersion();
      keggInfo.chargerState = info.getChargerStatus();
      lastKeggFirmwareVersion = keggInfo.fw + ' BL' + keggInfo.bl;

      const diag = info.getKegelDiagnostics();
      if (diag != null) {
        const extractedDiag = {
          boardRev: diag.getBoardRevision(),
          serialNumber: diag.getSerialNumber(),
          wdtApplied: diag.getWdtApplied(),
          overtemperatureApplied: diag.getOvertemperatureApplied(),
          lastChargingStartVoltage: diag.getLastChargingStartVoltage(),
          lastChargingTime: diag.getLastChargingTime(),
          numberOfChargings: diag.getNumberOfChargings(),
          lastMeasuredVoltage: diag.getLastMeasuredVoltage(),
          lastOperationTemperature: diag.getLastOperationTemperature(),
          mtuUsed: diag.getMtuUsed(),
          lastChargingTemperature: diag.getLastChargingTemperature(),
          lastConnectionIssue: diag.getLastConnectionIssue(),
          lastFaultReport: diag.getLastFaultReport(),
        };

        keggInfo.diagnostics = extractedDiag;
      }
    } catch (error) {
      console.log('Measurement deserialize error: ' + error);
    }

    keggInfos.push(keggInfo);
  }

  return { keggInfos, lastKeggFirmwareVersion };
};

const parseMeasurementData = (data: Uint8Array[]) => {
  const dataArray = [];
  const rpArray = [];
  const rnArray = [];
  let temperature = null;

  for (const sample of data) {
    let mbuff = null;
    let lm = null;
    try {
      mbuff = proto.KegelMeasurementBuffer.deserializeBinary(sample);
      lm = mbuff.getSingleMeasurementList();
    } catch (error) {
      console.log('Measurement deserialize error: ' + error);
    }

    if (lm && lm.length > 0) {
      for (const m of lm) {
        const x = {
          r: m.getResistance(),
          rp: m.getResistancePositive(),
          rn: m.getResistanceNegative(),
          pm: m.getPositiveMucus(),
          pms: m.getPositiveMucusS(),
          nm: -1 * m.getNegativeMucus(),
          nms: m.getNegativeMucusS(),
          pr: m.getPositiveReference(),
          prs: m.getPositiveReferenceS(),
          nr: -1 * m.getNegativeReference(),
          nrs: m.getNegativeReferenceS(),
        };

        if (m.getInternalTemperature() > 0) {
          temperature = m.getInternalTemperature();
        }

        rpArray.push(x.rp);
        rnArray.push(x.rn);
        dataArray.push(x);
      }
    }
  }
  return { dataArray, rpArray, rnArray, temperature };
};

const calculatePercentiles = (rpArray: number[], rnArray: number[]) => {
  let ten = 0,
    thirty = 0,
    fifty = 0,
    seventy = 0,
    ninety = 0;
  let tenN = 0,
    thirtyN = 0,
    fiftyN = 0,
    seventyN = 0,
    ninetyN = 0;

  const filteredRpArray = rpArray.filter(inRange);
  const filteredRnArray = rnArray.filter(inRange);

  if (filteredRpArray.length > 0) {
    ten = math.quantileSeq(filteredRpArray, 0.1);
    thirty = math.quantileSeq(filteredRpArray, 0.3);
    fifty = math.quantileSeq(filteredRpArray, 0.5);
    seventy = math.quantileSeq(filteredRpArray, 0.7);
    ninety = math.quantileSeq(filteredRpArray, 0.9);
  }

  if (filteredRnArray.length > 0) {
    tenN = math.quantileSeq(filteredRnArray, 0.1);
    thirtyN = math.quantileSeq(filteredRnArray, 0.3);
    fiftyN = math.quantileSeq(filteredRnArray, 0.5);
    seventyN = math.quantileSeq(filteredRnArray, 0.7);
    ninetyN = math.quantileSeq(filteredRnArray, 0.9);
  }

  const measurementValue = math.mean(fifty, fiftyN);
  const computedValues = {
    rp: { percentile10: ten, percentile30: thirty, percentile50: fifty, percentile70: seventy, percentile90: ninety },
    np: {
      percentile10: tenN,
      percentile30: thirtyN,
      percentile50: fiftyN,
      percentile70: seventyN,
      percentile90: ninetyN,
    },
  };

  return { measurementValue, computedValues };
};

const handlePreviousMeasurements = async (user: IUser, date: Date) => {
  const lastMeasurements = await Measurement.find({
    user: user._id,
    lastMeasurement: true,
    date: { $gte: moment(date).startOf('day'), $lt: moment(date).endOf('day') },
  });

  for (const previousMeasurement of lastMeasurements) {
    previousMeasurement.lastMeasurement = false;
    previousMeasurement.save();
  }
};

const updateUserInfo = async (
  user: IUser,
  newMeasurement: IMeasurement,
  data: AddMeasurementData,
  lastKeggFirmwareVersion: string,
  keggInfos: KeggInfo[],
) => {
  user.measurements.push(newMeasurement);
  if (!user.info) {
    user.info = {};
  }
  user.info.deviceInfo = data.phoneInfo;
  user.info.appVersion = data.appVersion;
  user.info.lastKeggFirmwareVersion = lastKeggFirmwareVersion;
  user.info.lastMeasurementDate = new Date(data.date).toISOString();

  //Temporary measure: Log if keggInfos is empty
  if (keggInfos.length === 0) {
    console.log('updateUserInfo Warning: KeggInfos is empty, user', user.id, user.email);
  }

  //Update serial number
  const rawSN = keggInfos[0]?.diagnostics?.serialNumber ?? null;

  if (typeof rawSN === 'number' || !isNaN(Number(rawSN))) {
    let snChange = false;
    // TODO: The format of the S/N might change when we find out how to make everything work in the manufacturing
    const sn = `K1${Number(rawSN).toString().padStart(6, '0')}A`;

    if (user.info.keggIdentifiers) {
      if (!user.info.keggIdentifiers.includes(sn)) {
        user.info.keggIdentifiers = [...user.info.keggIdentifiers, sn];
        snChange = true;
      }
    } else {
      user.info.keggIdentifiers = [sn];
      snChange = true;
    }
    if(snChange) {
      user_service.klaviyoSendEvent(user.email, 'kegg-serial-changed', 'Kegg device S/N change', {'keggSerial': sn});
    }
  }
  user.markModified('info');
  await user.save();
};

const handleCalendarItem = async (
  user: IUser,
  date: Date,
  measurementValue: number,
  newMeasurementId: string,
  resultCode: string,
) => {
  const dayToAddTo = moment.utc(date).format('YYYY-MM-DD');
  const time = moment.utc(date).format('HH:mm');
  const calendarItem = await Calendar.findOne({ user: user._id, date: dayToAddTo });

  if (resultCode == 'OK') {
    if (!calendarItem) {
      const day = {
        user: user._id,
        date: dayToAddTo,
        lastMeasurementTime: time,
        values: [measurementValue],
        measurements: [newMeasurementId],
      };
      await Calendar.create(day);
    } else {
      calendarItem.lastMeasurementTime = time;
      calendarItem.values.push(measurementValue);
      calendarItem.measurements.push(newMeasurementId);
      await calendarItem.save();
    }
  }
};

const sendEmail = (email: string, date: Date, measurement: unknown) => {
  let body = 'Hi,\n\n</br>';
  body +=
    'a new measurement has been recorded by ' +
    email +
    ' at ' +
    moment(date).format('MM/DD/YYYY HH:mm:ss') +
    '\n\n</br>';
  const attachment = JSON.stringify(measurement);
  Emailer.send(Consts.emailMeasurementsTo, 'kegg measurement', body, attachment, () => {});
};

const addMeasurement = async (
  data: AddMeasurementData,
  user: IUser,
  date: Date,
): Promise<{ success: boolean; message: string }> => {
  const { keggInfos, lastKeggFirmwareVersion } = parseKeggInfos(data.rawKeggInfos);
  const { dataArray, rpArray, rnArray, temperature } = parseMeasurementData(data.data);

  const { measurementValue, computedValues } = calculatePercentiles(rpArray, rnArray);

  const measurement = {
    date: data.date,
    keggInfos: keggInfos,
    rawKeggInfo: data.rawKeggInfo,
    rawKeggInfos: data.rawKeggInfos,
    keggId: data.keggId,
    phoneInfo: data.phoneInfo,
    sequenceInfo: data.sequenceInfo,
    resultCode: data.resultCode,
    protocolVersion: data.protocolVersion,
    appVersion: data.appVersion,
    firmwareVersion: lastKeggFirmwareVersion,
    user: user._id,
    data: dataArray,
    rawData: data.data,
    measurementValue,
    temperature,
    computedValues,
    session: data.session,
    lastMeasurement: true,
  };

  // Find previous last measurement based on the day
  await handlePreviousMeasurements(user, date);
  // Save new measurement
  const newMeasurement = await Measurement.create(measurement);
  await updateUserInfo(user, newMeasurement, data, lastKeggFirmwareVersion, keggInfos);

  // Add to calendar
  await handleCalendarItem(user, date, measurementValue, newMeasurement.id, data.resultCode);

  if (Consts.emailMeasurementsTo && Consts.emailMeasurementsTo.length > 0) {
    sendEmail(user.email, date, measurement);
  }

  console.log('Measurement added: ' + newMeasurement._id);
  return { success: true, message: 'Measurement saved' };
};

const updateMeasurement = async (
  id: string,
  data: MeasurementData[] | null,
): Promise<{ success: boolean; message: string }> => {
  const measurementToUpdate = await Measurement.findOne({ _id: id });
  if (measurementToUpdate == null) {
    return { success: false, message: 'Measurement does not exist' };
  }

  if (data) {
    measurementToUpdate.data = data;
  }
  await measurementToUpdate.save();
  return { success: true, message: 'Ok' };
};

const removeMeasurement = async (id: string) => {
  const existingMeasurement = await Measurement.findOne({ _id: id });
  if (!existingMeasurement) {
    console.log('Measurement does not exist: ' + id);
    return { success: false, message: 'Measurement does not exist' };
  }

  const existingCalendar = await Calendar.findOne({ user: existingMeasurement.user, measurements: id });
  if (!existingCalendar) {
    console.log('Calendar does not exist: ' + id);
    return { success: false, message: 'Calendar does not exist' };
  }
  const measurementIndex = existingCalendar.measurements.indexOf(id);

  if (existingCalendar.values.length == 1) {
    existingCalendar.values = [];
    existingCalendar.measurements = [];
  } else if (measurementIndex > -1) {
    existingCalendar.values.splice(measurementIndex, 1);
    existingCalendar.measurements.splice(measurementIndex, 1);
  }
  existingCalendar.save();

  await Measurement.deleteOne({ _id: id });
  await User.findOneAndUpdate({ _id: existingMeasurement.user }, { $pull: { measurements: id } });

  console.log('Measurement removed: ' + existingMeasurement._id);
  return { success: true, message: 'Ok' };
};

const exportMeasurements = async (query: { user?: string; _id?: string }) => {
  const measurements = await Measurement.find(query, {}, null);
  let csvContent = '';

  measurements.forEach((measurement: IMeasurement) => {
    csvContent += 'r,rp,rn,pm,nm id=' + measurement._id + '\n';
    measurement.data.forEach((d: MeasurementData) => {
      csvContent += (d.rp + d.rn) / 2 + ',' + d.rp + ',' + d.rn + ',' + d.pm + ',' + d.nm + '\n';
    });
    csvContent += '\n';
  });

  return csvContent;
};

const exportAggregate = (
  measurements: IMeasurement[],
  reqValues: { name: string; percentile: number }[] | null,
): string => {
  let csvContent = '';

  let values;
  if (reqValues != null) {
    values = reqValues;
  } else {
    values = [
      { name: 'rp', percentile: 10 },
      { name: 'rp', percentile: 30 },
      { name: 'rp', percentile: 50 },
      { name: 'rp', percentile: 70 },
      { name: 'rp', percentile: 90 },
      { name: 'rn', percentile: 10 },
      { name: 'rn', percentile: 30 },
      { name: 'rn', percentile: 50 },
      { name: 'rn', percentile: 70 },
      { name: 'rn', percentile: 90 },
    ];
  }

  type Value = {
    name: string;
    percentile: number;
  };

  values.forEach((value: Value) => {
    csvContent += `${value.name}${value.percentile},`;
  });
  csvContent += 'id\n';

  measurements.forEach((measurement: IMeasurement) => {
    values.forEach((value: Value) => {
      const data: number[] = [];
      measurement.data.forEach((d: MeasurementData) => {
        data.push(d[value.name as keyof MeasurementData]);
      });
      data.sort((a, b) => a - b);
      const index = Math.ceil((value.percentile * data.length) / 100) - 1;

      csvContent += `${data[index]},`;
    });
    csvContent += `${measurement._id}\n`;
  });

  return csvContent;
};

export default {
  getMeasurements,
  addMeasurement,
  updateMeasurement,
  removeMeasurement,
  exportMeasurements,
  exportAggregate,
};
