import KeggTest from '@/models/keggTest';
import { IKeggTest } from '@/types/keggTest';
import moment from 'moment';

export type GetKeggTestDataParams = {
  page?: number;
  pagesize?: number;
  sortBy?: string;
  sort?: number;
};

const getKeggTestData = async ({
  page = 0,
  pagesize = 20,
  sortBy = 'date',
  sort = -1,
}: GetKeggTestDataParams): Promise<{
  success: boolean;
  pagination: {
    page: number;
    pagesize: number;
    total: number;
  };
  data: {
    date: Date;
    success: boolean;
    serial: number;
    ip?: string;
    result: object;
  }[];
}> => {
  const limits = {
    skip: page * pagesize,
    limit: pagesize,
    sort: { [sortBy]: sort },
  };

  const [keggTestItems, total] = await Promise.all([
    KeggTest.find({ success: { $ne: null } }, {}, limits),
    KeggTest.countDocuments({ success: { $ne: null } }),
  ]);

  const data = keggTestItems.map((item) => ({
    date: item.date,
    success: item.success,
    serial: item.serial,
    ip: item.ip,
    result: item.result,
  }));

  return {
    success: true,
    pagination: { page, pagesize, total },
    data,
  };
};

export type AddKeggTestDataParams = {
  serial: number;
  success: boolean;
  result: object;
  ip?: string;
};

const addKeggTestData = async ({
  serial,
  success,
  result,
  ip,
}: AddKeggTestDataParams): Promise<{ success: boolean; message: string }> => {
  const existingKeggTest = await KeggTest.findOne({ serial });
  if (existingKeggTest == null) {
    const newKeggTest = {
      serial,
      success,
      result,
      ip,
    };
    await KeggTest.create(newKeggTest);
  } else {
    existingKeggTest.date = new Date();
    existingKeggTest.success = success;
    existingKeggTest.result = result;
    existingKeggTest.ip = ip;
    await existingKeggTest.save();
  }
  return { success: true, message: 'Ok' };
};

const exportKeggTestData = async (): Promise<{ ret: string }> => {
  const tests = await KeggTest.find({ success: { $ne: null } }).sort({ date: -1 });

  const header = 'ts;success;serial;ip;data\n';

  const rows = tests.map((test) =>
    [
      moment(test.date).format('MM/DD/YYYY HH:mm:ss'),
      test.success,
      test.serial,
      test.ip ?? '', // Handle null or undefined IP
      JSON.stringify(test.result),
    ].join(';'),
  );

  const ret = header + rows.join('\n') + '\n';

  return { ret };
};

export const createEmptyTest = async (): Promise<{
  success: boolean;
  data?: IKeggTest;
  message?: string;
  error?: string;
}> => {
  const emptyTest = await KeggTest.create({});
  return { success: true, message: 'Ok', data: emptyTest };
};

export default {
  getKeggTestData,
  addKeggTestData,
  exportKeggTestData,
  createEmptyTest,
};
