import fs from 'fs';
import yaml from 'js-yaml';
import path from 'path';
import swagger<PERSON><PERSON><PERSON>, { Options } from 'swagger-jsdoc';

const options: Options = {
  definition: {
    openapi: '3.0.1',
    info: {
      title: 'Kegg API',
      version: '1.1.0',
      description: 'API documentation for Kegg API',
    },
    components: {
      parameters: {
        AccessToken: {
          name: 'x-access-token',
          in: 'header',
          description: 'Access Token',
          required: true,
          schema: {
            type: 'string',
          },
        },
      },
      schemas: {
        UserDataV2: {
            type: 'object',
            properties: {
                id: {
                    type: 'string',
                    description: 'The unique user identifier.',
                },
                email: {
                    type: 'string',
                    description: "The user's email address.",
                },
                firstName: {
                    type: 'string',
                    description: "The user's first name.",
                },
                lastName: {
                    type: 'string',
                    description: "The user's last name.",
                },
                birthDate: {
                    type: 'string',
                    format: 'date',
                    description: "The user's birth date in YYYY-MM-DD format.",
                },
                height: {
                    type: 'string',
                    description: "The user's height (in metric units).",
                },
                weight: {
                    type: 'string',
                    description: "The user's weight (in metric units).",
                },
                consent: {
                    type: 'object',
                    properties: {
                        research: {
                            type: 'boolean',
                        },
                        keggPlus: {
                            type: 'boolean',
                        },
                    },
                    description: "Consent details.",
                },
                trackingReason: {
                    type: 'array',
                    items: {
                        type: 'string',
                    },
                    description: "List of tracking reasons (e.g. getPregnant, trackMyCycle, exerciseMyPelvicFloor).",
                },
                cycleType: {
                    type: 'string',
                    enum: ['regular', 'irregular'],
                    description: "The user's cycle type.",
                },
                initialCycleLength: {
                    type: 'string',
                    description: "The initial cycle length.",
                },
                initialPeriodLength: {
                    type: 'string',
                    description: "The initial period length.",
                },
                conditions: {
                    type: 'array',
                    items: {
                        type: 'string',
                    },
                    description: "List of user conditions.",
                },
                lastUnfinishedCycleStart: {
                    type: 'string',
                    format: 'date-time',
                    description: "Start date of the last unfinished cycle.",
                },
                medications: {
                    type: 'array',
                    items: {
                        type: 'string',
                    },
                    description: "List of medications.",
                },
                emailVerified: {
                    type: 'boolean',
                    description: "Whether the user's email is verified.",
                },
                keggIdentifiers: {
                    type: 'string',
                    description: "KEGG device identifier.",
                },
            },
          },
        },
    },
  },
  apis: ['./dist/routes/*.js'],
};

const swaggerSpec = swaggerJSDoc(options);
const outputPath = path.resolve(__dirname, 'swaggerSpec.yaml');
fs.writeFileSync(outputPath, yaml.dump(swaggerSpec), 'utf-8');
console.log('Swagger specification file generated successfully at', outputPath);

export default swaggerSpec;
