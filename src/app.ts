import dotenv from 'dotenv';
import express, { Request, Response } from 'express';
import http from 'http';
import createError from 'http-errors';
import mongoose from 'mongoose';
import path from 'path';
import swaggerUi from 'swagger-ui-express';

import { Consts } from './consts';
import calendarRoute from './routes/calendar';
import cycleRoute from './routes/cycle';
import externalConnectionRoute from './routes/externalConnection';
import healthRoute from './routes/health';
import indexRoute from './routes/index';
import keggTestRoute from './routes/keggTest';
import measurementRoute from './routes/measurement';
import predictionRoute from './routes/prediction';
import statisticsRoute from './routes/statistics';
import userRoute from './routes/user';
import swaggerSpec from './swagger';
import calendarService from './services/calendar_service';

dotenv.config();

console.log(
  'Starting ' + Consts.appName + ' ' + Consts.apiVersion + ' ' + Consts.environment + ' ' + Consts.deployTimestamp,
);

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(express.static(path.join(__dirname, 'public')));

app.use((_req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, x-access-token');
  next();
});

const httpServer = http.createServer(app);
httpServer.listen(process.env.PORT || '80');

mongoose.connect(Consts.dbConnectionString, {
  tlsAllowInvalidCertificates: true,
  tlsAllowInvalidHostnames: true,
}
);

const db = mongoose.connection;
db.on('error', (err) => console.error('Database connection error:', err));
db.once('open', () => {
  console.log('Database connection opened successfully');
});

app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));

// Define routes
app.use('/', indexRoute);
app.use('/health', healthRoute);
app.use('/user', userRoute);
app.use('/measurement', measurementRoute);
app.use('/cycle', cycleRoute);
app.use('/calendar', calendarRoute);
app.use('/prediction', predictionRoute);
app.use('/prediction/input', predictionRoute);
app.use('/keggtest', keggTestRoute);
app.use('/statistics', statisticsRoute);
app.use('/externalConnection', externalConnectionRoute);

// Catch 404 and forward to error handler
app.use((_req, _res, next) => {
  next(createError(404));
});

// Error handler middleware
app.use((err: Error & { status?: number }, _req: Request, res: Response, _next: any) => {
  const statusCode = 'status' in err && typeof err.status === 'number' ? err.status : 500;

  // In non-production environments, include the error stack.
  if (Consts.environment === 'production') {
    // In production or other environments, only send a generic error message
    res.status(statusCode).json({
      error: 'Kegg server is sad. An unknown error has occured. Please try again later or try something else.',
    });
    console.log('Error:', err.message); // Log the error message for internal debugging
  } else {
    console.error('Error:', err.stack); // Log the stack trace for internal debugging
    res.status(statusCode).json({
      error: err.message,
      stack: err.stack,
    });
  }
});

console.log(
  'Started ' + Consts.appName + ' ' + Consts.apiVersion + ' ' + Consts.environment + ' ' + Consts.deployTimestamp,
);

// Add this conditional to only run in specific environments if needed
  console.log('Initiating ad-hoc statistics generation...');
  // Run with a slight delay to ensure app is fully initialized
  setTimeout(() => {
    calendarService.generateAdHocStatistics()
      .then(() => console.log('Ad-hoc statistics generation completed'))
      .catch(err => console.error('Failed to generate ad-hoc statistics:', err));
  }, 5000);

export default app;
