import { Document, ObjectId } from 'mongoose';

export interface IMeasurement extends Document<string | ObjectId> {
  user: string | ObjectId;
  date: Date;
  serverDate: Date;
  keggId: string;
  keggInfos: object[];
  sequenceInfo: object;
  resultCode: string;
  phoneInfo: {
    platform: string;
    os: string;
    brand: string;
    model: string;
  };
  protocolVersion: string;
  appVersion: string;
  firmwareVersion: string;
  temperature: number | null;
  data: MeasurementData[];
  rawData: object[];
  resistanceAvg: number;
  measurementValue: number;
  computedValues: object;
  session: object[];
  lastMeasurement: boolean;
}

export type MeasurementData = {
  rp: number;
  rn: number;
  pm: number;
  nm: number;
};

export type KeggInfo = {
  date: Date;
  raw: Uint8Array;
  battery?: number;
  fw?: string;
  bl?: number;
  chargerState?: number;
  diagnostics?: {
    boardRev: number;
    serialNumber: number;
    wdtApplied: number;
    overtemperatureApplied: number;
    lastChargingStartVoltage: number;
    lastChargingTime: number;
    numberOfChargings: number;
    lastMeasuredVoltage: number;
    lastOperationTemperature: number;
    mtuUsed: number;
    lastChargingTemperature: number;
    lastConnectionIssue: number;
    lastFaultReport: number;
  };
};
