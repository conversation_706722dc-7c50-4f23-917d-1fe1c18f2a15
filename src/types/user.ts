import { Document, ObjectId } from 'mongoose';
import { ICalendar } from './calendar';
import { IMeasurement } from './measurements';

export type TrackingReason = 'trying_to_conceive' | 'cycle_info_tracking_fertility' | 'not_specified' | 'not_sure_yet';
export type TrackingReasonV2 = ('getPregnant' | 'exerciseMyPelvicFloor' | 'trackMyCycle')[];

export type Consent = {
  research?: boolean;
  keggPlus?: boolean; //Added in the new app
};

export type TryingToConceiveDuration = 'just_started' | '1_to_3' | '3_to_6' | '6_to_9' | '9_to_12' | '12_plus';

export type RegularCycleType = 'regular' | 'iregular' | 'not_specified';

export interface IUser extends Document<string | ObjectId> {
  email: string;
  name: string;
  password: string;
  created: Date;
  lastActivity: Date;
  enabled: boolean;
  emailVerified: boolean;
  test: boolean;
  role: string;
  phone: string;
  group: string;
  img: { data: Buffer; contentType: string };
  measurementsCount: number;
  calendarCount: number;
  info?: UserInfo | null;
  token: string | null;
  linkedAccount: {
    type: string;
    id: string;
    token: string;
  };
  tempPasswordToken: string | null;
  tempPasswordTokenValidity: Date | null;
  measurements: IMeasurement[];
  calendar: ICalendar[];
  cycleCount: number;
}

export type SignUpData = { //Actually, a UserInfo.profile data
  consent: Consent; //Additionally, we have a keggPlus field in the new ap
  keggIdentifier: string; //this is actually empty for existing users, S/Ns are written to UserInfo.keggIdentifiers as a string array
  firstName: string;
  lastName: string; //Last name not present in the new app
  dateOfBirth: string | null; // actually a YYYY birth year (sometimes empty), the new app changes this to a full YYYY-MM-DD date
  weight: string | null; //see below
  height: string | null;  //weight and height are a string in app locale units!! we should be converting everything on the app side between units and save here only in metric system.
  trackingReason: TrackingReason | null; //Deprecated, we have a trackingReason array in the new app
  tryingToConceiveDuration: TryingToConceiveDuration | null; //deprecated AFAIK
  initialStartOfPeriod: string;
  initialCycleLength: string;
  regularCycle: RegularCycleType | null; //Deprecated: cycleType in the new app, the only difference is that we do not have not_specified anymore,
  supplements: string | null; //Deprecated, we will use "medications" from now on. It is questionable if we should even try to migrate this
                              //as it is a simple text field where users can write anything.
  timeOfMeasurement: string | null;
  supplementsHistory: Record<string, string> | null; //Also deprecated...
  //New App only:
  initialPeriodLength: string | null; //Default to 5 for existing users during migration, ideally ask them to provide it
  conditions: Condition[] | null; //Default to empty array for existing users during migration, ideally ask them to provide it
  trackingReasonV2: TrackingReasonV2 | null; //Changed from exclusive to multiple choice. Mapping to the old data only for Insight
  medications: Medication[] | null; //Hard/impossible to migrate from supplements. Ideally ask them to provide it
  cycleType: 'regular' | 'irregular' ; //Default migration should be based on current data
  profileVersion: number | null; //2 for the new app, 1 for the old one - can be used i.e. to check whether the user has already been migrated
};
// TEMPORARY TRANSITIONAL TYPES ---------------------------------------
export type Condition = [
  'anovulation',
  'endometriosis',
  'pcos',
  'hypo',
  'hypothalamicAmenorrhea',
  'pregnancyLoss',
  'hormoneImbalance',
  'breastfeeding',
  'elevatedProlactin',
  'breastCancer',
  'gynecologicalCancer',
  'postLEEP',
  'recentlyStoppedBirthControl',
  'miscarriageHistory',
  'adenomyosis',
  'prematureOvarianFailure',
];

export type Medication = [
  'letrozole',
  'metformin',
  'clomid',
  'myoinositol',
  'prenatal',
];

export type UserDataV2 = { //Basically partially a SignUpData (UserInfo.profile) + some data from IUser. We need to map this manually right now
  id: string;
  email?: string;
  firstName: string;
  lastName: string;
  birthDate: string; //now it is a YYYY-MM-DD string instead of YYYY
  height: string;
  weight: string;
  consent: Consent;
  trackingReason?: TrackingReasonV2
  cycleType?: 'regular' | 'irregular';
  initialCycleLength?: string; //keep the old name
  initialPeriodLength?: string; //consistent naming here
  conditions?: Condition[];
  lastUnfinishedCycleStart?: string; //keep the old name
  medications?: Medication[];
  emailVerified?: boolean; //How are we going to use this one?
  keggIdentifiers?: string; //if it is possible to read during "pairing," we should use this! Default format: K[S/N from the device]A
  profileVersion?: number; //2 for the new app, 1 for the old one - can be used i.e. to check whether the user has already been migrated
};
// END OF TEMPORARY TRANSITIONAL TYPES ---------------------------------------

export type StatisticsData = {
  totalRegistered: number;
  usersByPlatform: {
    ios: number;
    android: number;
    others: number;
  };
  usersByAge: {
    users_18_24: number;
    users_25_34: number;
    users_35_44: number;
    users_45_54: number;
    users_55_64: number;
    users_65: number;
  };
  usersByPurpose: {
    trying_to_conceive: number;
    cycle_info_tracking_fertility: number;
    not_sure_yet: number;
  };
  totalWithMoreThanOneMeasurement: number;
  totalWithMoreThanTwoExercises: number;
};

export type UserInfo = {
  language?: string;
  appVersion?: string;
  lastKeggFirmwareVersion?: string;
  deviceInfo?: DeviceInfo;
  profile?: Partial<SignUpData>;
  note?: string;
  lastUnfinishedCycleStart?: string;
  keggIdentifiers?: string[];
  lastFinishedCycleLength?: string;
  lastMeasurementDate?: string;
};

export type DeviceInfo = {
  platform: string;
  os: string;
  brand: string;
  model: string;
};
