import { Document, ObjectId } from 'mongoose';

export type InfectionType = 'uti' | 'yeast' | 'bacterial_vaginosis' | 'other';
export type CervicalMucusType = 'none' | 'sticky' | 'creamy' | 'slippery' | 'watery';
export type SensationType = 'dry' | 'moist' | 'lubricative';
export type SexDriveType = 'low' | 'moderate' | 'high';
export type TemperatureType = number | { source: string; value: number };
export type ExclusionReason =
  | 'Illness'
  | 'Suspect no ovulation'
  | 'Lactating'
  | 'Medication'
  | 'Pregnancy'
  | 'Pregnancy loss'
  | 'Vaginal infection'
  | 'Other';
export type ForecastMethods = 'counting' | 'kegg';

export interface ICalendar extends Document<string | ObjectId> {
  user: ObjectId;
  date: Date;
  type: string;
  data: CalendarDayData | null;
  values: number[];
  lastMeasurementTime: string;
  measurements: string[];
}

export type CalendarDayData = {
  period?: boolean;
  spotting?: boolean;
  lhTest?: boolean;
  progTest?: boolean; //DEPRECATED by new APP
  pregnancy?: boolean;
  sex?: 0 | 1 | '0' | '1';
  temperature?: TemperatureType;
  cervicalMucus?: CervicalMucusType;
  sensation?: SensationType; //DEPRECATED by new APP
  sexDrive?: SexDriveType; //DEPRECATED by new APP
  infection?: InfectionType;
  notes?: string;
  excluded?: boolean;
  exclusionReason?: ExclusionReason | null;
  periodStart?: boolean | null;
  periodBleeding?: 'low' | 'medium' | 'high'; //INTRODUCED in new APP
  dailySupplements?: boolean; //INTRODUCED in new APP
  exercise?: boolean; //INTRODUCED in new APP
};

export interface PastData {
  fertile?: boolean;
  method?: ForecastMethods;
  ovulation?: boolean;
  beforeOvulation?: boolean;
  afterOvulation?: boolean;
}

export type CalendarDay = {
  _id?: ObjectId;
  date: string;
  prediction?: CalendarDayPrediction | null;
  period?: boolean;
  data?: CalendarDayData;
  past?: PastData;
  lastMeasurementTime?: string;
  values?: number[];
};

export interface CalendarDayPrediction {
  period?: boolean;
  periodStart?: boolean;
  beforeOvulation?: boolean;
  fertile?: boolean;
  fertileWindow?: boolean;
  ovulation?: boolean;
  afterOvulation?: boolean;
  fertilityProbability?: number;
  ovulationProbability?: number;
  futureCycle?: boolean;
  ovDay?: boolean;
}

export type AndromedaPredictionsData = { 
  fertilityProbability: number;
  ovulationPropability: number;
  isFertile: boolean;
  isFertileWindow: boolean;
  isPeriod: boolean;
  isTrueOvulation: boolean;
  isFutureCycle: boolean;
  isOVDay: boolean;
};

export type CycleDay = {
  calendarId: string | ObjectId;
  day: string;
  val: number;
  lastMeasurementTime: string;
};

export type Cycle = {
  startDate: string;
  days: CycleDay[];
  length?: number;
  finished: boolean;
  excluded?: boolean;
  exclusionReason?: string;
  periodLength?: number;
};

export type CalendarData = {
  days: Record<string, CalendarDay>;
  data: CalendarDay[];
  lastPeriodStartDay: string | null;
  nextPeriodStartDay: string | null;
};

export type OvulationData = {
  period_start: Date;
  ovulation: Date;
  fertility_window: {
    start: Date;
    end: Date;
  };
  method: ForecastMethods;
};

export type ForecastData = {
  period_start: Date;
  ovulation: Date;
  fertility_window: {
    start: Date;
    end: Date;
  };
  method: ForecastMethods;
};

