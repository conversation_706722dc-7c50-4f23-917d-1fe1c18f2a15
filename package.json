{"name": "kegg", "version": "2.0.10", "private": true, "scripts": {"start": "node dist/app.js", "dev": "nodemon src/app.ts", "build": "tsc --build && tsc-alias", "swagger": "node ./swagger.ts", "lint": "eslint src/**/*.ts", "format": "eslint src/**/*.ts --fix"}, "dependencies": {"@aws-sdk/client-s3": "^3.744.0", "@aws-sdk/client-ses": "^3.744.0", "bcrypt": "^5.1.1", "dotenv": "^16.4.7", "express": "^4.21.2", "file-system": "^1.2.2", "google-auth-library": "^9.15.1", "google-protobuf": "^3.21.4", "http-auth": "^4.2.0", "http-errors": "~2.0.0", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "mathjs": "^14.2.1", "mixpanel": "^0.18.0", "moment": "^2.30.1", "mongodb": "^6.13.0", "mongoose": "^8.10.0", "mongoose-sequence": "^6.0.1", "nodemon": "^3.1.9", "ofetch": "^1.4.1", "path": "^0.12.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "ts-node": "^10.9.2", "tsc-alias": "^1.8.10", "tsconfig-paths": "^4.2.0", "verify-apple-id-token": "^3.1.2"}, "devDependencies": {"@eslint/js": "^9.20.0", "@types/bcrypt": "^5.0.2", "@types/eslint__js": "^8.42.3", "@types/express": "^5.0.0", "@types/http-errors": "^2.0.4", "@types/js-yaml": "^4.0.9", "@types/jsonwebtoken": "^9.0.8", "@types/mongoose-sequence": "^3.0.11", "@types/node": "^22.13.1", "@types/node-fetch": "^2.6.12", "@types/request": "^2.48.12", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.7", "eslint": "^9.20.0", "i": "^0.3.7", "prettier": "^3.5.0", "typescript": "^5.7.3", "typescript-eslint": "^8.23.0"}, "nodemonConfig": {"watch": ["src"], "ext": "ts", "exec": "node -r tsconfig-paths/register -r ts-node/register ./src/app.ts"}}